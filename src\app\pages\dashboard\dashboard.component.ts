import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntil } from 'rxjs';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { DashboardData, UpcomingBirthday } from './models';
import { DashboardService } from './services';
import { AuthService } from 'src/app/auth/services';
import { CommonUtils } from 'src/app/shared/utils';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse } from 'src/app/shared/models';
import { PlanStatus } from '../plans-and-passes/models';
import { MaintenanceRequestsState } from '../requests/pages/maintenance-request/models';
import { ClassTypes, ScheduleDetailsView } from '../scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { EChartsOption } from 'echarts';
import { LeaveStatus } from '../requests/pages/leave-request/models';
import { ClientDashboardComponent } from './pages/client-dashboard/client-dashboard.component';
import { ROLE_IDS } from 'src/app/shared/constants';
import { PaymentService } from '../schedule-classes/services';
import { BillStatus, UserBillingTransactionsResponse } from '../billing/models';

const DEPENDENCIES = {
  MODULES: [CommonModule, SharedModule, MatButtonModule, MatIconModule, MatSelectModule, MatTooltipModule, FormsModule, MatTooltipModule],
  COMPONENTS: [ClientDashboardComponent]
};

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent extends BaseComponent implements OnInit {
  today = new Date();
  dashboardData!: DashboardData;
  todayBirthdays!: Array<UpcomingBirthday>;
  upcomingBirthdays!: Array<UpcomingBirthday>;
  scheduleLessonDetails!: Array<ScheduleDetailsView>;
  pendingPayments: Array<UserBillingTransactionsResponse> = [];
  chartOptions!: EChartsOption;
  dailyWorkingHoursChartOptions!: EChartsOption;
  monthlyWorkingHoursChartOptions!: EChartsOption;
  availableLeaves!: number;
  usedLeaves!: number;
  totalLeaves!: number;
  totalDailyWorkingHours!: number;
  completedDailyWorkingHours!: number;
  pendingDailyWorkingHours!: number;
  totalMonthlyWorkingHours!: number;
  completedMonthlyWorkingHours!: number;
  pendingMonthlyWorkingHours!: number;
  planCancelStatus = PlanStatus;
  maintenanceRequestStatus = MaintenanceRequestsState;
  classTypes = ClassTypes;
  roleIds = ROLE_IDS;
  isOnLeaveCardCollapsed = false;
  showAllOnLeave = false;
  showSupervisorSchedule = false;
  showSupervisorDailyWorkingHours = false;
  showSupervisorMonthlyWorkingHours = false;
  selectedDependentId!: number;

  constructor(
    private readonly dashboardService: DashboardService,
    private readonly paymentService: PaymentService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
    this.getCurrentId();
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          if (this.currentUser?.userRoleId === this.constants.roleIds.ADMIN || this.currentUser?.userRoleId === this.constants.roleIds.DESK_MANAGER) {
            this.getPendingPayments();
          }
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentId(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.dependentId) {
        this.selectedDependentId = +params.dependentId;
      } else {
        this.selectedDependentId = 0;
      }
      this.getDashboardData(this.selectedDependentId);
    });
  }

  getDashboardData(dependentId: number): void {
    this.showPageLoader = true;
    const filter = dependentId ? `?DependentIdFilter=${dependentId}` : '';
    this.dashboardService
      .getList<CBGetResponse<DashboardData>>(`${API_URL.octopusDashBoard.getDashboardDetails}${filter}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.dashboardData = res.result;
          this.setBirthdayData();
          this.setScheduleData();
          this.setDailyWorkingHoursData();
          this.setMonthlyWorkingHoursData();
          this.setLeavesData();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      Page: 1,
      PageSize: 2,
      RecurringBillStatus: BillStatus.OPEN
    });
  }

  getPendingPayments(): void {
    this.paymentService
      .getListWithFilters<CBResponse<UserBillingTransactionsResponse>>(this.getFilterParams(), API_URL.payment.getAllBillingDetailsOfUser)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<UserBillingTransactionsResponse>) => {
          this.pendingPayments = res.result.items;
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  setBirthdayData(): void {
    if (!this.dashboardData.upcomingBirthdays) {
      return;
    }
    this.upcomingBirthdays = this.dashboardData.upcomingBirthdays.filter(birthday => birthday.isUpcomingBirthday);
    this.todayBirthdays = this.dashboardData.upcomingBirthdays.filter(birthday => !birthday.isUpcomingBirthday);
  }

  setLeavesData(): void {
    if (!this.dashboardData.leaveBalance) {
      return;
    }
    if (this.dashboardData.leaveBalance.length) {
      this.totalLeaves = this.dashboardData.leaveBalance[0].totalLeaveDays;
      this.usedLeaves = this.dashboardData.leaveBalance[0].usedLeaveDays;
      this.availableLeaves = this.dashboardData.leaveBalance[0].remainingLeaveDays;
      this.chartOptions = CommonUtils.getChartOptions(this.availableLeaves, this.usedLeaves, this.totalLeaves);
    }
  }

  setScheduleData(selectedOption = false): void {
    this.showSupervisorSchedule = selectedOption;
    if (this.dashboardData.scheduleLessonDetails) {
      this.scheduleLessonDetails = this.dashboardData.scheduleLessonDetails;
    } else {
      const scheduleData = selectedOption
        ? this.dashboardData.instructorScheduleLessonDetails
        : this.dashboardData.supervisorScheduleLessonDetails;
      this.scheduleLessonDetails = scheduleData;
    }
  }

  setDailyWorkingHoursData(selectedOption = false): void {
    this.showSupervisorDailyWorkingHours = selectedOption;
    if (this.dashboardData.dailyWorkingHours) {
      this.totalDailyWorkingHours = this.dashboardData.dailyWorkingHours.totalWorkingHours;
      this.completedDailyWorkingHours = this.dashboardData.dailyWorkingHours.totalActualHours;
      this.pendingDailyWorkingHours = this.totalDailyWorkingHours - this.completedDailyWorkingHours;
      this.dailyWorkingHoursChartOptions = CommonUtils.getWorkingHoursChartOptions(
        this.completedDailyWorkingHours,
        this.pendingDailyWorkingHours,
        this.totalDailyWorkingHours
      );
    }
  }

  setMonthlyWorkingHoursData(selectedOption = false): void {
    this.showSupervisorMonthlyWorkingHours = selectedOption;
    if (this.dashboardData.mounthlyWorkingHours) {
      this.totalMonthlyWorkingHours = this.dashboardData.mounthlyWorkingHours.totalWorkingHours;
      this.completedMonthlyWorkingHours = this.dashboardData.mounthlyWorkingHours.totalActualHours;
      this.pendingMonthlyWorkingHours = this.totalMonthlyWorkingHours - this.completedMonthlyWorkingHours;
      this.monthlyWorkingHoursChartOptions = CommonUtils.getWorkingHoursChartOptions(
        this.completedMonthlyWorkingHours,
        this.pendingMonthlyWorkingHours,
        this.totalMonthlyWorkingHours
      );
    }
  }

  formatHoursToHM(decimalHours: number): string {
    return CommonUtils.formatHoursToHM(decimalHours);
  }

  getInitialsUsingFullName(name: string): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  getInitials(firstName: string, lastName: string): string {
    return CommonUtils.getInitials(firstName, lastName);
  }

  getTimeDiff(start: string, end: string): number | null {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  getTimeBasedGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 18) return 'Good Afternoon';
    return 'Good Evening';
  }

  getStatusClass(status: number | undefined): string {
    switch (status) {
      case 1:
        return 'achPending';
      case 2:
        return 'enrolled';
      case 3:
        return 'notEnrolled';
      default:
        return '';
    }
  }

  navigateTo(path: string, queryParams = false): void {
    this.router.navigate([path], queryParams ? { queryParams: { activeTab: 'My Leave Request', mode: 'add' } } : {});
  }

  navigateToLeaveRequest(): void {
    if (this.currentUser?.userRoleId === this.constants.roleIds.ADMIN) {
      this.router.navigate(['/request/leave-request'], {
        queryParams: { activeTab: 'My Leave Request', status: LeaveStatus.PENDING_ACTION }
      });
    } else {
      this.router.navigate(['/request/leave-request'], {
        queryParams: { activeTab: 'Other Leave Request', status: LeaveStatus.PENDING_ACTION }
      });
    }
  }

  navigateToStudentViewPage(studentId: number): void {
    this.router.navigate([this.path.members.root, this.path.members.clients], { queryParams: { dependentId: studentId } });
  }

  shouldShowDivider(totalItemsLength: number, index: number, showAll: boolean, minShow: number): boolean {
    if (index === totalItemsLength - 1) {
      return false;
    }

    if (!showAll) {
      return index < minShow - 1;
    }

    return true;
  }
}
