import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FrequentlyAskedQuestions } from '../../models';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { HelpPageCreation } from 'src/app/pages/settings/pages/help-page-creation/models/help-page-creation.model';
import { MatDialog } from '@angular/material/dialog';
import { takeUntil } from 'rxjs';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { AppToasterService } from 'src/app/shared/services';
import { HelpService } from '../../services';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogRes } from 'src/app/shared/models';

const DEPENDENCIES = {
  MODULES: [CommonModule, SharedModule, MatTooltipModule],
  COMPONENTS: []
};

@Component({
  selector: 'app-frequently-asked-questions',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './frequently-asked-questions.component.html',
  styleUrl: './frequently-asked-questions.component.scss'
})
export class FrequentlyAskedQuestionsComponent extends BaseComponent implements OnChanges {
  @Input() faqs!: Array<FrequentlyAskedQuestions>;
  @Input() override showPageLoader!: boolean;

  selectedFAQIds: Array<number> = [];

  @Output() openEditFAQ = new EventEmitter<FrequentlyAskedQuestions>();
  @Output() updateFAQs = new EventEmitter<void>();

  constructor(
    private readonly dialog: MatDialog,
    private readonly helpService: HelpService,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['faqs']?.currentValue) {
      this.faqs = changes['faqs']?.currentValue;
      this.openFAQsByDefault();
    }
  }

  openFAQsByDefault(): void {
    if (this.faqs.length) {
      this.selectedFAQIds.push(this.faqs[0].id);
    }
  }

  toggleFAQs(id: number): void {
    if (this.selectedFAQIds.includes(id)) {
      this.selectedFAQIds = this.selectedFAQIds.filter((selectedId) => selectedId !== id);
      return;
    }
    this.selectedFAQIds.push(id);
  }

  isFAQAnswerVisible(id: number): boolean {
    return this.selectedFAQIds.includes(id);
  }

  openEditFAQFun(faq: FrequentlyAskedQuestions): void {
    this.openEditFAQ.emit(faq);
  }

  deleteFAQConfirmation(FAQId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete FAQ`,
        message: `Are you sure you want to delete this FAQ?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteFAQ(FAQId);
      }
    });
  }

  deleteFAQ(FAQId: number): void {
    this.helpService
      .delete(FAQId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.updateFAQs.emit();
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'FAQ'));
        }
      });
  }
}
