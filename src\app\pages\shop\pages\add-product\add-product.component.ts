import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, FormArray, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { UploadFileService } from 'src/app/shared/services/upload-file.service';
import { CBGetResponse, CBResponse, FileUpload } from 'src/app/shared/models';
import { CommonModule } from '@angular/common';
import { RevenueCategory } from 'src/app/pages/settings/pages/revenue-categories/models';
import { AddProductForm, StoreProductDetails, StoreProductQuantities, StoreProductQuantitiesForm } from '../../models';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { StoreProductService } from '../../services';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    SharedModule,
    MatSelectModule
  ]
};

@Component({
  selector: 'app-add-product',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './add-product.component.html',
  styleUrl: './add-product.component.scss'
})
export class AddProductComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedProductId!: number | undefined;

  productFormGroup!: FormGroup<AddProductForm>;
  isShowUploadSpinner!: boolean;
  revenueCategories!: Array<RevenueCategory>;
  locations!: Array<SchoolLocations>;
  productDetail!: StoreProductDetails;
  fullPath!: string;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() productAddedorEdited = new EventEmitter<number | undefined>();

  constructor(
    private readonly storeProductService: StoreProductService,
    private readonly toasterService: AppToasterService,
    private readonly uploadFileService: UploadFileService,
    private readonly commonService: CommonService,
    private readonly dialog: MatDialog,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initProductForm();
    this.getRevenueCategories();
    this.getLocations();
    if (!this.selectedProductId) {
      this.addNewStoreProductQuantities();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedProductId']?.currentValue) {
      this.selectedProductId = changes['selectedProductId'].currentValue;
      this.getProductDetailById(this.selectedProductId!);
    }
  }

  initProductForm(): void {
    this.productFormGroup = new FormGroup<AddProductForm>({
      productName: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      productDescription: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      productImage: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      productRevenueCategoryId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      productPrice: new FormControl(undefined, { nonNullable: true, validators: [Validators.required, Validators.min(1)] }),
      storeProductQuantities: new FormArray([] as FormGroup<StoreProductQuantitiesForm>[]),
      id: new FormControl(0, { nonNullable: true })
    });
  }

  getStoreProductQuantitiesFormArray(): FormArray {
    return this.productFormGroup?.get('storeProductQuantities') as FormArray;
  }

  setStoreProduct(storeProducts: StoreProductQuantities[] | undefined): void {
    this.getStoreProductQuantitiesFormArray().clear();
    storeProducts?.forEach(storeProduct => {
      this.addNewStoreProductQuantities(storeProduct);
    });
  }

  addNewStoreProductQuantities(quantity?: StoreProductQuantities): void {
    const formGroup = new FormGroup<Record<string, AbstractControl>>({
      productId: new FormControl(quantity?.productId ?? 0, { nonNullable: true }),
      totalQuantity: new FormControl(quantity?.totalQuantity ?? undefined, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(1)]
      }),
      locationId: new FormControl(quantity?.locationId ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      availableQuantity: new FormControl(quantity?.availableQuantity ?? 0, {
        nonNullable: true,
        validators: [Validators.min(0)]
      })
    });

    this.getStoreProductQuantitiesFormArray()?.push(formGroup);
  }

  getProductDetailById(id: number | null): void {
    this.showPageLoader = true;
    this.storeProductService
      .getListWithFilters<CBGetResponse<StoreProductDetails>>({ id: id }, `${API_URL.storeProduct.getStoreProductForView}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<StoreProductDetails>) => {
          this.productDetail = res.result;
          this.fullPath = this.productDetail.productImage ?? '';
          this.productFormGroup?.patchValue({
            ...this.productDetail,
            productImage: this.productDetail.productRelativePath
          });
          this.setStoreProduct(this.productDetail?.storeProductQuantities);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  confirmationPopup(index: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Quantity Detail`,
        message: `Are you sure you want to delete this quantity information?`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result.isConfirmed) {
        this.getStoreProductQuantitiesFormArray().removeAt(index);
        this.cdr.detectChanges();
      }
    });
  }

  getRevenueCategories(): void {
    this.commonService
      .getRevenueCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RevenueCategory>) => {
          this.revenueCategories = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    const fileExtension = file?.name.split('.').pop()?.toLowerCase();

    if (!file) {
      return;
    }

    if (!this.constants.allowedExtensionsImage.includes(fileExtension || '')) {
      this.toasterService.error(this.constants.errorMessages.fileUploadRestrict.replace('{item}', 'Image'));
      return;
    }

    if (file.size > this.constants.maxFileSizes.MAX_FILE_SIZE_2MB) {
      this.toasterService.error(this.constants.errorMessages.fileSizeExceeds.replace('{item}', 'Image').replace('{size}', '2MB'));
      return;
    }

    this.uploadFileToAws(file);

    if (input.files && input.files.length > 0 && input.files[0].size <= this.constants.maxFileSizes.MAX_FILE_SIZE_5MB) {
      this.uploadFileToAws(input.files[0]);
    } else {
      this.toasterService.error(this.constants.errorMessages.fileSizeExceeds.replace('{item}', 'Image').replace('{size}', '5MB'));
    }
  }

  onFileDropped(file: File): void {
    this.uploadFileToAws(file);
  }

  uploadFileToAws(params: File): void {
    this.isShowUploadSpinner = true;
    this.uploadFileService
      .uploadFile(API_URL.uploadFile.uploadFileToS3, this.getDocumentParams(params))
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<FileUpload>) => {
          if (res.result?.fullPath && res.result.shortPath) {
            this.fullPath = res.result.fullPath;
            this.productFormGroup.controls.productImage.setValue(res.result.shortPath);
            this.toasterService.success(this.constants.successMessages.fileUploadSuccess);
          }
          this.isShowUploadSpinner = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.isShowUploadSpinner = false;
          this.cdr.detectChanges();
        }
      });
  }

  getDocumentParams(selectedFile: File): FormData {
    const formData = new FormData();
    formData.append('file', selectedFile, selectedFile.name);
    formData.append('uploadType', '5');
    return formData;
  }

  onInvalidFile(invalidMessage: string): void {
    this.toasterService.error(invalidMessage);
  }

  onRemoveUploadedFileConfirmation(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Remove Uploaded File',
        message: 'Are you sure you want to remove uploaded file?'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result.isConfirmed) {
        this.removeFileFromAws();
      }
    });
  }

  removeFileFromAws(): void {
    this.uploadFileService
      .deleteFile(
        `${API_URL.uploadFile.deleteFileFromAws}?${API_URL.uploadFile.fileName}=${this.productFormGroup.controls.productImage.value}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.productFormGroup.controls.productImage.setValue('');
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  calculateAvailableQuantity(index: number): void {
    const formArray = this.getStoreProductQuantitiesFormArray();
    const totalQuantity = formArray.at(index).get('totalQuantity')?.value;
    const soldQuantity = this.productDetail?.storeProductQuantities[index]?.soldQuantity ?? 0;
    const calculatedAvailableQuantity = totalQuantity - soldQuantity;
    formArray.at(index).get('availableQuantity')?.setValue(calculatedAvailableQuantity);
  }

  getAvailableQuantity(index: number): number {
    const formArray = this.getStoreProductQuantitiesFormArray();
    return formArray.at(index).get('availableQuantity')?.value ?? 0;
  }

  getSoldQuantity(index: number): number {
    return this.productDetail?.storeProductQuantities[index]?.soldQuantity ?? 0;
  }

  onSubmit(): void {
    if (this.productFormGroup.invalid) {
      this.productFormGroup.markAllAsTouched();
      return;
    }
    this.productFormGroup.markAsUntouched();
    this.showBtnLoader = true;
    this.storeProductService
      .add(this.productFormGroup.getRawValue(), API_URL.crud.createOrUpdate)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.selectedProductId
            ? this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Product'))
            : this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'Product'));
          this.closeSideNavFun();
          this.productAddedorEdited.emit(this.selectedProductId);
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavFun(): void {
    this.productFormGroup.reset();
    this.closeSideNav.emit();
  }
}
