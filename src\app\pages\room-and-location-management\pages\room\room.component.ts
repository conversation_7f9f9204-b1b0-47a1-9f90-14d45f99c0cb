import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { CBResponse, IdNameModel, MatDialogRes } from 'src/app/shared/models';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSidenavModule } from '@angular/material/sidenav';
import { Debounce } from 'src/app/shared/decorators';
import { NgxPaginationModule } from 'ngx-pagination';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { AppToasterService } from 'src/app/shared/services';
import { FiltersEnum, RoomDetails, RoomFilters, RoomInfo, SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { RoomService } from './services';
import { MatSelectModule } from '@angular/material/select';
import { AuthService } from 'src/app/auth/services';
import { CommonUtils } from 'src/app/shared/utils';
import { DashIfEmptyPipe } from 'src/app/shared/pipe';
import { MultiSelectComponent } from 'src/app/shared/components/multi-select/multi-select.component';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    MatIconModule,
    MatInputModule,
    MatSidenavModule,
    NgxPaginationModule,
    CommonModule,
    MatSelectModule,
    SharedModule
  ],
  PIPES: [DashIfEmptyPipe],
  COMPONENTS: [MultiSelectComponent]
};
@Component({
  selector: 'app-room',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './room.component.html',
  styleUrl: './room.component.scss'
})
export class RoomComponent extends BaseComponent implements OnInit, OnChanges {
  roomDetails!: RoomDetails[];
  schoolLocations!: Array<SchoolLocations>;
  AllLocation = FiltersEnum;
  totalCount!: number;
  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  filters: RoomFilters = {
    searchTerm: null,
    locationId: {
      id: 1,
      defaultPlaceholder: 'All Locations',
      placeholder: 'All Locations',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    }
  };
  @Output() roomInfo = new EventEmitter<RoomInfo | null>();
  @Output() roomView = new EventEmitter<RoomDetails>();
  @Input() roomAddedOrEdited!: boolean;

  constructor(
    private readonly roomService: RoomService,
    private readonly authService: AuthService,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getRoomDetails(this.currentPage, this.pageSize);
    this.getAllLocations();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['roomAddedOrEdited']?.currentValue) {
      this.roomAddedOrEdited = changes['roomAddedOrEdited'].currentValue;
      this.getRoomDetails(this.currentPage, this.pageSize);
    }
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getRoomDetails(this.currentPage, this.pageSize);
  }

  getAllLocations(): void {
    this.authService
      .getAllLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.schoolLocations = res.result.items;
          this.filters.locationId.options = res.result.items.map(location => ({
            id: location.schoolLocations.id,
            name: location.schoolLocations.locationName
          }));
          this.filters.locationId.totalCount = this.schoolLocations.length;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParams(currentPage: number, pageSize: number) {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      filter: this.filters.searchTerm,
      locationIdFilter: [...this.filters.locationId.value],
      page: currentPage,
      pageSize: pageSize
    });
  }

  getRoomDetails(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    this.roomService
      .add(this.getFilterParams(currentPage, pageSize), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RoomDetails>) => {
          this.roomDetails = res.result.items;
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  deleteRoomConfirmation(roomId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Room`,
        message: `Are you sure you want to delete this Room?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteRoom(roomId);
      }
    });
  }

  deleteRoom(roomId: number): void {
    this.roomService
      .delete(roomId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Room'));
          this.getRoomDetails(this.currentPage, this.pageSize);
        }
      });
  }

  toggleViewRoom(roomViewDetails: RoomDetails): void {
    this.roomView.emit(roomViewDetails);
  }

  toggleEditRoom(roomDetails: RoomInfo | null): void {
    this.roomInfo.emit(roomDetails);
    this.cdr.detectChanges();
  }

  @Debounce(300)
  onSearchTermChanged(): void {
    this.currentPage = 1;
    this.getRoomDetails(this.currentPage, this.pageSize);
  }
}
