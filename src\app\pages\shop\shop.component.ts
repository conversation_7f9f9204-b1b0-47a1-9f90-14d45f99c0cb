import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { NgxPaginationModule } from 'ngx-pagination';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { Debounce } from 'src/app/shared/decorators';
import { CBResponse } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CommonUtils } from 'src/app/shared/utils';
import { InstructorFilters } from '../members/pages/instructors/models';
import { StoreProductService } from './services';
import { StoreProductDetails } from './models';
import { AddProductComponent } from './pages/add-product/add-product.component';
import { ViewProductComponent } from './pages/view-product/view-product.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { AppToasterService } from 'src/app/shared/services';
import { AssignProductFilter } from '../members/pages/students/models';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    MatIconModule,
    MatInputModule,
    MatSidenavModule,
    NgxPaginationModule,
    CommonModule,
    SharedModule
  ],
  COMPONENTS: [AddProductComponent, ViewProductComponent]
};

@Component({
  selector: 'app-shop',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './shop.component.html',
  styleUrl: './shop.component.scss'
})
export class ShopComponent extends BaseComponent implements OnInit {
  totalCount!: number;
  storeProducts!: Array<StoreProductDetails>;

  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  isAddProductSideNavOpen = false;
  isViewProductSideNavOpen = false;
  selectedProductId!: number | undefined;

  filters: AssignProductFilter = {
    searchTerm: null
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly storeProductService: StoreProductService,
    private readonly toasterService: AppToasterService,
    private readonly dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {
    this.getStoreProducts(this.currentPage, this.pageSize);
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getStoreProducts(this.currentPage, this.pageSize);
  }

  getStoreProducts(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();

    this.storeProductService
      .getListWithFiltersWithPagination<CBResponse<StoreProductDetails>>(
        this.getFilterParams(),
        currentPage,
        pageSize,
        `${API_URL.crud.getAll}`,
        false
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StoreProductDetails>) => {
          this.totalCount = res.result.totalCount;
          this.storeProducts = res.result.items;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      Filter: this.filters.searchTerm
    });
  }

  toggleSideNav(type: 'add' | 'view', isOpen: boolean, productId: number | undefined): void {
    if (type === 'add') {
      this.isAddProductSideNavOpen = isOpen;
    } else if (type === 'view') {
      this.isViewProductSideNavOpen = isOpen;
    }
    this.selectedProductId = productId;
  }

  confirmationPopup(id: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Product`,
        message: `Are you sure you want to delete this product?`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result.isConfirmed) {
        this.deleteProduct(id);
        this.cdr.detectChanges();
      }
    });
  }

  deleteProduct(id: number): void {
    this.storeProductService.delete(id, API_URL.crud.delete).subscribe({
      next: () => {
        this.isAddProductSideNavOpen = false;
        this.isViewProductSideNavOpen = false;
        this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Product'));
        this.getStoreProducts((this.currentPage = 1), this.pageSize);
        this.cdr.detectChanges();
      }
    });
  }

  @Debounce(300)
  onSearchTermChanged(): void {
    this.currentPage = 1;
    this.getStoreProducts(this.currentPage, this.pageSize);
  }
}
