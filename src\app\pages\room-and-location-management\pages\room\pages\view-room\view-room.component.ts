import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { RoomDetails, RoomInfo } from 'src/app/pages/room-and-location-management/models';
import { MatButtonModule } from '@angular/material/button';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { RoomService } from '../../services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService } from 'src/app/shared/services';
import { CBGetResponse, MatDialogRes } from 'src/app/shared/models';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule]
};
@Component({
  selector: 'app-view-room',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './view-room.component.html',
  styleUrl: './view-room.component.scss'
})
export class ViewRoomComponent extends BaseComponent implements OnChanges {
  @Input() selectedRoomViewDetails!: RoomDetails | null;

  @Output() closeViewSideNav = new EventEmitter<void>();
  @Output() roomUpdated = new EventEmitter<boolean>();
  @Output() openSideNav = new EventEmitter<RoomInfo>();

  constructor(
    private readonly dialog: MatDialog,
    private readonly roomService: RoomService,
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedRoomViewDetails']?.currentValue) {
      this.getRoomDetailById();
    }
  }

  getRoomDetailById(): void {
    this.showPageLoader = true;
    this.roomService
      .getList<CBGetResponse<RoomDetails>>(
        `${API_URL.roomDetails.getRoomDetailForView}?id=${this.selectedRoomViewDetails?.roomDetail.id}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.selectedRoomViewDetails = res.result;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  deleteRoomConfirmation(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Room`,
        message: `Are you sure you want to delete this Room?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteRoom(this.selectedRoomViewDetails!.roomDetail.id);
      }
    });
  }

  deleteRoom(roomId: number): void {
    this.roomUpdated.emit(false);
    this.roomService
      .delete(roomId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Room'));
          this.roomUpdated.emit(true);
          this.closeViewSideNavFun();
          this.cdr.detectChanges();
        }
      });
  }

  navigateToEdit(): void {
    this.openSideNav.emit(this.selectedRoomViewDetails?.roomDetail);
  }

  closeViewSideNavFun(): void {
    this.closeViewSideNav.emit();
  }
}
