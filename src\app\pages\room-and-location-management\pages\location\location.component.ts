import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { CBResponse, MatDialogRes } from 'src/app/shared/models';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSidenavModule } from '@angular/material/sidenav';
import { Debounce } from 'src/app/shared/decorators';
import { NgxPaginationModule } from 'ngx-pagination';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { AppToasterService } from 'src/app/shared/services';
import { LocationDetails, SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { LocationService } from './services';
import { Router } from '@angular/router';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    MatIconModule,
    MatInputModule,
    MatSidenavModule,
    NgxPaginationModule,
    CommonModule,
    SharedModule
  ]
};
@Component({
  selector: 'app-location',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './location.component.html',
  styleUrl: './location.component.scss'
})
export class LocationComponent extends BaseComponent implements OnInit, OnChanges {
  searchTerm!: string;
  schoolLocations!: SchoolLocations[];
  totalCount!: number;
  @Output() locationDetails = new EventEmitter<LocationDetails | null>();
  @Input() locationAddedOrEdited!: boolean;

  constructor(
    private readonly locationService: LocationService,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getLocationDetails();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['locationAddedOrEdited']?.currentValue) {
      this.locationAddedOrEdited = changes['locationAddedOrEdited'].currentValue;
      this.getLocationDetails();
    }
  }

  getLocationDetails(searchName?: string): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    const filter = searchName ? `?Filter=${searchName}` : '';
    this.locationService
      .getList<CBResponse<SchoolLocations>>(`${API_URL.crud.getAll}${filter}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.schoolLocations = res.result.items;
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  deleteLocationConfirmation(locationId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Location`,
        message: `Are you sure you want to delete this Location?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteLocation(locationId);
      }
    });
  }

  deleteLocation(locationId: number): void {
    this.locationService
      .delete(locationId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Location'));
          this.getLocationDetails();
          this.cdr.detectChanges();
        }
      });
  }

  toggleEditLocation(LocationDetails: LocationDetails | null): void {
    this.locationDetails.emit(LocationDetails);
  }

  redirectToRoomSchedule(locationId: number): void {
    this.router.navigate([this.path.schedule.root], {
      queryParams: { activeTab: 'Room Schedule', locationId: locationId }
    });
  }

  @Debounce(300)
  onSearchTermChanged(): void {
    this.getLocationDetails(this.searchTerm);
  }
}
