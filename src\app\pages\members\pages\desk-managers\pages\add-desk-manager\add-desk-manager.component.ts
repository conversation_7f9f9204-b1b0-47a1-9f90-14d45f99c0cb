import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import {
  AbstractControl,
  AbstractControlOptions,
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CommonModule, DatePipe } from '@angular/common';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { CBGetResponse, CBResponse, MatDialogRes } from 'src/app/shared/models';
import { AuthService } from 'src/app/auth/services';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { EnumToKeyValuePipe } from 'src/app/shared/pipe';
import moment from 'moment';
import {
  dateOfBirthValidator,
  outOfRangeTimeValidator,
  phoneNumberValidator,
  timeRangeValidator,
  usedLeaveDaysValidator,
  zipCodeValidator
} from 'src/app/shared/validators';
import {
  GradeLevel,
  AvailabilityType,
  InstructorAvailabilityFormGroup,
  InstructorAvaibilityInInstructorDetail,
  LeaveBalanceFormGroup,
  LabelValueKey
} from '../../../instructors/models';
import { DeskManagerDetails, DeskManagerFormGroup } from '../../models';
import { DeskManagerDetailsService } from '../../services';
import { State } from 'src/app/auth/models/user.model';
import { LeaveType, LeaveBalance, MemberDayViseLeave } from 'src/app/pages/requests/pages/leave-request/models';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    SharedModule,
    MatSelectModule,
    NgxMaskDirective,
    CommonModule,
    MatCheckboxModule,
    MatDatepickerModule,
    NgxMaterialTimepickerModule
  ],
  PIPES: [EnumToKeyValuePipe]
};

@Component({
  selector: 'app-add-desk-manager',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  providers: [provideNgxMask(), provideNativeDateAdapter()],
  templateUrl: './add-desk-manager.component.html',
  styleUrl: '../../../instructors/pages/add-instructor/add-instructor.component.scss'
})
export class AddDeskManagerComponent extends BaseComponent implements OnInit {
  @Input() selectedDeskManagerDetails!: DeskManagerDetails | null;

  deskManagerFormGroup!: FormGroup<DeskManagerFormGroup>;
  locations!: Array<SchoolLocations>;
  gradeLevels!: Array<GradeLevel>;
  states!: Array<State>;
  maxDate = new Date();
  availabilityTypes = AvailabilityType;
  leaveTypes = LeaveType;
  leaveData!: MemberDayViseLeave[];

  @Output() closeSideNav = new EventEmitter<DeskManagerDetails | null>();
  @Output() isInstructorAdded = new EventEmitter<void>();

  constructor(
    private readonly deskManagerDetailsService: DeskManagerDetailsService,
    private readonly authService: AuthService,
    private readonly toasterService: AppToasterService,
    private readonly commonService: CommonService,
    private readonly dialog: MatDialog,
    private readonly datePipe: DatePipe,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initDeskManagerForm();
    if (!this.selectedDeskManagerDetails) {
      this.addNewLocationAndInstructorAvailability();
      this.addLeaveBalance(this.leaveObject(LeaveType.PAID));
      this.addLeaveBalance(this.leaveObject(LeaveType.UNPAID));
    }
    this.getAllLocations();
    this.getAllStates();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedDeskManagerDetails']?.currentValue) {
      this.showPageLoader = true;
      this.selectedDeskManagerDetails = changes['selectedDeskManagerDetails'].currentValue;
    }
  }

  ngAfterViewInit(): void {
    this.setDeskManagerForm();
  }

  initDeskManagerForm(): void {
    this.deskManagerFormGroup = new FormGroup<DeskManagerFormGroup>({
      id: new FormControl(undefined, { nonNullable: true }),
      name: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.NAME_PATTERN)]
      }),
      email: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.EMAIL)]
      }),
      phoneNumber: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, phoneNumberValidator()]
      }),
      address: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      stateId: new FormControl(undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      city: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      zipCode: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, zipCodeValidator()]
      }),
      dateOfBirth: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, dateOfBirthValidator()]
      }),
      roleId: new FormControl(this.constants.roleIds.DESK_MANAGER, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      deskManagerAvailabilityAndLocations: new FormArray([] as FormGroup<InstructorAvailabilityFormGroup>[]),
      leaveBalances: new FormArray([] as FormGroup<LeaveBalanceFormGroup>[])
    });
  }

  setDeskManagerForm(): void {
    if (this.selectedDeskManagerDetails) {
      this.deskManagerFormGroup?.patchValue({ ...this.selectedDeskManagerDetails });
      this.deskManagerFormGroup?.get('email')?.disable();
      this.setLocationAndAvailability(this.selectedDeskManagerDetails.deskManagerAvailabilityAndLocations);
      this.setLeaveBalance(this.selectedDeskManagerDetails.leaveBalances);
      this.cdr.detectChanges();
    }
  }

  setLocationAndAvailability(deskManagerAvailabilityAndLocations: InstructorAvaibilityInInstructorDetail[] | undefined): void {
    const availabilityFormArray = this.getInstructorFormArray('deskManagerAvailabilityAndLocations');
    availabilityFormArray.clear();
    deskManagerAvailabilityAndLocations?.forEach((availability, i) => {
      this.addNewLocationAndInstructorAvailability(availability, i);
      availability.availableDays?.forEach(day => this.setDaysOfWeek(day, i));
      if (this.selectedDeskManagerDetails) {
        const lastIndex = availabilityFormArray.length - 1;
        availabilityFormArray.at(lastIndex).get('locationsId')?.disable();
      }
    });
    this.showPageLoader = false;
  }

  setLeaveBalance(leaveBalances: LeaveBalance[] | undefined): void {
    const leaveBalanceFormArray = this.getInstructorFormArray('leaveBalances');
    leaveBalanceFormArray.clear();
    if (leaveBalances && leaveBalances.length) {
      leaveBalances[0].memberDayViseLeaves?.forEach(leave => {
        this.leaveData = this.leaveData || [];
        this.leaveData.push({
          day: leave.day,
          usedLeaveDays: leave.usedLeaveDays,
          remainingLeaveDays: leave.remainingLeaveDays,
          totalLeaveDays: leave.totalLeaveDays
        });
      });
    }
    leaveBalances?.forEach(leave => {
      this.addLeaveBalance(leave);
    });
    this.cdr.detectChanges();
  }

  addLeaveBalance(leave?: LeaveBalance): void {
    const formGroup = new FormGroup<Record<string, AbstractControl>>({
      leaveType: new FormControl(leave?.leaveType ?? 1, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      totalLeaveDays: new FormControl(leave?.totalLeaveDays ?? 0, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(0)]
      }),
      usedLeaveDays: new FormControl(leave?.usedLeaveDays ?? 0, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(0), usedLeaveDaysValidator()]
      }),
      remainingLeaveDays: new FormControl(leave?.remainingLeaveDays ?? 0, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(0)]
      })
    });

    if (leave?.leaveType === this.leaveTypes.UNPAID) {
      formGroup.get('totalLeaveDays')?.disable();
    }

    // Subscribe to leaveType changes
    formGroup.get('leaveType')?.valueChanges.subscribe(type => {
      if (type === 1) {
        formGroup.get('totalLeaveDays')?.enable();
      } else {
        formGroup.get('totalLeaveDays')?.disable();
        formGroup.get('totalLeaveDays')?.setValue(0);
      }
    });

    // Subscribe to totalLeaveDays changes to update validation
    formGroup.get('totalLeaveDays')?.valueChanges.subscribe(() => {
      // Trigger validation on usedLeaveDays when totalLeaveDays changes
      formGroup.get('usedLeaveDays')?.updateValueAndValidity();
    });

    this.getInstructorFormArray('leaveBalances')?.push(formGroup);
  }

  getRemainingLeaveDays(index: number): number {
    const group = this.getInstructorFormArray('leaveBalances').at(index) as FormGroup;
    const total = group.get('totalLeaveDays')?.value || 0;
    const used = group.get('usedLeaveDays')?.value || 0;
    const remaining = Math.max(total - used, 0);

    // Update the remainingLeaveDays control
    group.get('remainingLeaveDays')?.setValue(remaining);

    return remaining;
  }

  leaveObject(leaveType: number): LeaveBalance {
    return {
      leaveType: leaveType,
      remainingLeaveDays: 0,
      totalLeaveDays: 0,
      usedLeaveDays: 0,
      memberDayViseLeaves: [
        {
          day: 0,
          usedLeaveDays: 0,
          remainingLeaveDays: 0,
          totalLeaveDays: 0
        }
      ]
    };
  }

  getMinStartDate(index: number): Date {
    const availabilityControl = this.getInstructorFormArray('deskManagerAvailabilityAndLocations').at(index);
    const availableStartDate = availabilityControl.get('availableStartDate')?.value;

    const startDate = availableStartDate ? new Date(availableStartDate) : this.maxDate;
    return startDate > this.maxDate ? this.maxDate : startDate;
  }

  resetValues(index: number): void {
    this.getInstructorFormArray('deskManagerAvailabilityAndLocations').at(index)?.get('availabilityType')?.setValue(null);
    this.getInstructorFormArray('deskManagerAvailabilityAndLocations').at(index)?.get('availableEndDate')?.setValue(null);
    this.getInstructorFormArray('deskManagerAvailabilityAndLocations').at(index)?.get('neverEnd')?.setValue(null);
    (this.getInstructorFormArray('deskManagerAvailabilityAndLocations').at(index)?.get('availableDays') as FormArray).clear();
  }

  getInstructorFormArray(controlName: string): FormArray {
    return this.deskManagerFormGroup?.get(controlName) as FormArray;
  }

  getFormattedStartTime(i: number): string {
    return (
      this.datePipe.transform(this.selectedDeskManagerDetails?.deskManagerAvailabilityAndLocations[i]?.availableStartTime, 'shortTime') ??
      ''
    );
  }

  getFormattedEndTime(i: number): string {
    return (
      this.datePipe.transform(this.selectedDeskManagerDetails?.deskManagerAvailabilityAndLocations[i]?.availableEndTime, 'shortTime') ?? ''
    );
  }

  addNewLocationAndInstructorAvailability(deskManagerAvailabilityAndLocations?: InstructorAvaibilityInInstructorDetail, i?: number): void {
    const endDate = this.datePipe.transform(deskManagerAvailabilityAndLocations?.availableEndDate, this.constants.dateFormats.yyyy_MM_dd);

    this.getInstructorFormArray('deskManagerAvailabilityAndLocations')?.push(
      new FormGroup(
        {
          id: new FormControl(0, { nonNullable: true }),
          availableStartTime: new FormControl(deskManagerAvailabilityAndLocations?.availableStartTime ?? '', {
            nonNullable: true,
            validators: [Validators.required, outOfRangeTimeValidator()]
          }),
          availableEndTime: new FormControl(deskManagerAvailabilityAndLocations?.availableEndTime ?? '', {
            nonNullable: true,
            validators: [Validators.required, outOfRangeTimeValidator()]
          }),
          availableStartDate: new FormControl(deskManagerAvailabilityAndLocations?.availableStartDate ?? '', {
            nonNullable: true,
            validators: [Validators.required]
          }),
          availableEndDate: new FormControl(deskManagerAvailabilityAndLocations?.availableEndDate ?? '', {
            nonNullable: true
          }),
          availableDays: new FormArray([] as FormControl<number>[]),
          locationsId: new FormControl(deskManagerAvailabilityAndLocations?.locationsId ?? undefined, {
            nonNullable: true,
            validators: [Validators.required]
          }),
          deskManagerId: new FormControl(this.selectedDeskManagerDetails?.id ?? 0, {
            nonNullable: true
          }),
          availabilityType: new FormControl(deskManagerAvailabilityAndLocations?.availabilityType ?? undefined, {
            nonNullable: true,
            validators: [Validators.required]
          }),
          neverEnd: new FormControl(endDate === this.getOneYearLaterDate(deskManagerAvailabilityAndLocations?.availableStartDate), {
            nonNullable: true
          })
        },
        { validators: timeRangeValidator('availableStartTime', 'availableEndTime') } as AbstractControlOptions
      )
    );
  }

  getAllLocations(): void {
    this.authService
      .getAllLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getAllStates(): void {
    this.commonService
      .getStates()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Array<State>>) => {
          this.states = res.result;
          this.cdr.detectChanges();
        }
      });
  }

  setFormControlValue(controlName: string, value: number | string | boolean | Date, i: number): void {
    const instructorForm = this.getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i];
    instructorForm?.get(controlName)?.setValue(value);
    if (controlName === 'availabilityType') {
      this.setAvailabilityType(value, i);
    }
    if (controlName === 'availableEndDate') {
      instructorForm?.get('neverEnd')?.setValue(value === this.getOneYearLaterDate(instructorForm?.get('availableStartDate')?.value));
    }
  }

  setAvailabilityType(value: number | string | boolean | Date, i: number): void {
    const deskManagerAvailabilityAndLocations = this.getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i];
    if (value === this.availabilityTypes.DAILY || value === this.availabilityTypes.WEEKLY) {
      this.setRequiredBasedOnCondition('availableEndDate', true, i);
    }
    if (value === this.availabilityTypes.WEEKLY) {
      this.setRequiredBasedOnCondition('availableDays', true, i);
    }
    if (value === this.availabilityTypes.DAILY) {
      this.setRequiredBasedOnCondition('availableDays', false, i);
      const availableDaysArray = deskManagerAvailabilityAndLocations.get('availableDays') as FormArray;
      availableDaysArray.clear();
      this.constants.daysOfTheWeek.forEach(day =>
        availableDaysArray.push(new FormControl(day.value, { nonNullable: true }))
      );
    }
    if (value === this.availabilityTypes.NO_REPEATS) {
      this.setRequiredBasedOnCondition('availableDays', false, i);
      deskManagerAvailabilityAndLocations
        .get('availableEndDate')
        ?.setValue(deskManagerAvailabilityAndLocations.get('availableStartDate')?.value);
      (deskManagerAvailabilityAndLocations.get('availableDays') as FormArray).clear();
    }
  }

  getOneYearLaterDate(startDate?: string): string {
    return moment(startDate).add(1, 'year').format(this.constants.dateFormats.yyyy_MM_DD);
  }

  setDaysOfWeek(dayValue: number, i: number): void {
    const daysOfSchedule = this.getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get('availableDays') as FormArray;
    const index = daysOfSchedule.value.indexOf(dayValue);

    if (index !== -1) {
      daysOfSchedule.removeAt(index);
    } else {
      daysOfSchedule.push(new FormControl(dayValue, { nonNullable: true }));
    }
  }

  isDaySelected(dayValue: number, i: number): boolean | undefined {
    const daysOfSchedule = this.getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get('availableDays')?.value;
    return daysOfSchedule?.includes(dayValue);
  }

  setTime(control: string, i: number): void {
    const deskManagerAvailabilityAndLocations = this.getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i]?.get(
      control
    );
    const currentDate = this.datePipe.transform(
      this.selectedDeskManagerDetails?.deskManagerAvailabilityAndLocations[i]?.availableStartDate
        ? this.selectedDeskManagerDetails.deskManagerAvailabilityAndLocations[i]?.availableStartDate
        : this.maxDate,
      this.constants.dateFormats.yyyy_MM_dd
    );

    const timeValue = this.datePipe.transform(
      new Date(`${currentDate} ${deskManagerAvailabilityAndLocations?.value}`),
      this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
    );
    deskManagerAvailabilityAndLocations?.setValue(timeValue ?? '');
  }

  setDate(control: string, i: number): void {
    this.setFormControlValue(
      control,
      this.datePipe.transform(
        this.getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i]?.get(control)?.value,
        this.constants.dateFormats.yyyy_MM_dd
      )!,
      i
    );
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean, i: number): void {
    const control = this.getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  confirmationPopup(index: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Location & Availability`,
        message: `Are you sure you want to delete this Instrument Location & Availability?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.getInstructorFormArray('deskManagerAvailabilityAndLocations').removeAt(index);
        this.cdr.detectChanges();
      }
    });
  }

  setRemainingLeaveDays(): void {
    const leaveBalances = this.getInstructorFormArray('leaveBalances').controls;
    leaveBalances.forEach((leave, i) => leave.get('remainingLeaveDays')?.setValue(this.getRemainingLeaveDays(i)));
  }

  showLeaveBalance(): boolean {
    return this.getInstructorFormArray('deskManagerAvailabilityAndLocations').controls.some(
      ctrl =>
        ctrl.get('availabilityType')?.value === this.availabilityTypes.DAILY ||
        (ctrl.get('availabilityType')?.value === this.availabilityTypes.WEEKLY && ctrl.get('availableDays')?.value.length)
    );
  }

  getLeaveData(day: number): any {
    return this.leaveData.find(item => item.day === day);
  }

  setLeaveData(leaveData: MemberDayViseLeave, controlName: any, event: Event): void {
    const day: any = this.leaveData.find(item => item.day === leaveData.day);
    day[controlName] = parseFloat((event.target as HTMLInputElement).value) || 0;
    day.remainingLeaveDays = Math.max(day.totalLeaveDays - day.usedLeaveDays, 0);
  }

  getDaysForTimeOff(): Array<LabelValueKey> {
    const availabilities = this.getInstructorFormArray('deskManagerAvailabilityAndLocations').controls;
    const hasDaily = availabilities.some(ctrl => ctrl.get('availabilityType')?.value === this.availabilityTypes.DAILY);
    if (hasDaily) {
      this.ensureLeaveDataForDays(this.constants.daysOfTheWeek);
      return this.constants.daysOfTheWeek;
    }

    const allSelectedDays = new Set<number>();
    availabilities.forEach(ctrl => {
      const days: number[] = ctrl.get('availableDays')?.value || [];
      days.forEach(day => allSelectedDays.add(day));
    });
    this.ensureLeaveDataForDays(Array.from(allSelectedDays).map(day => ({ value: day })));
    return this.constants.daysOfTheWeek.filter(day => allSelectedDays.has(day.value));
  }

  ensureLeaveDataForDays(days: Array<{ value: number }>): void {
    if (!Array.isArray(this.leaveData)) {
      this.leaveData = [];
    }
    days.forEach(dayObj => {
      const exists = this.leaveData.some(ld => ld.day === dayObj.value);
      if (!exists) {
        this.leaveData.push({
          day: dayObj.value,
          usedLeaveDays: 0,
          remainingLeaveDays: 0,
          totalLeaveDays: 0
        });
      }
    });
    this.leaveData = this.leaveData.filter(ld => days.some(dayObj => dayObj.value === ld.day));
  }

  capUsedLeave(leaveData: any, event?: Event): void {
    if (leaveData.usedLeaveDays > leaveData.totalLeaveDays) {
      leaveData.usedLeaveDays = leaveData.totalLeaveDays;
      if (event) {
        (event.target as HTMLInputElement).value = leaveData.totalLeaveDays.toString();
      }
    }
  }

  get getLeaveBalanceDto(): LeaveBalance[] {
    if (this.showLeaveBalance()) {
      return [
        {
          leaveType: LeaveType.PAID,
          totalLeaveDays: this.leaveData.reduce((acc, item) => acc + item.totalLeaveDays, 0),
          usedLeaveDays: this.leaveData.reduce((acc, item) => acc + item.usedLeaveDays, 0),
          remainingLeaveDays: this.leaveData.reduce((acc, item) => acc + item.remainingLeaveDays, 0),
          memberDayViseLeaves: this.leaveData.map(item => ({
            day: item.day,
            usedLeaveDays: item.usedLeaveDays,
            remainingLeaveDays: item.remainingLeaveDays,
            totalLeaveDays: item.totalLeaveDays
          }))
        }
      ];
    }
    return [
      {
        leaveType: LeaveType.PAID,
        totalLeaveDays: 0,
        usedLeaveDays: 0,
        remainingLeaveDays: 0,
        memberDayViseLeaves: []
      }
    ];
  }

  onSubmit(): void {
    if (this.deskManagerFormGroup.invalid) {
      this.deskManagerFormGroup.markAllAsTouched();
      return;
    }
    this.deskManagerFormGroup.markAsUntouched();
    this.showBtnLoader = true;
    this.setRemainingLeaveDays();
    this.deskManagerDetailsService
      .add(
        {
          ...this.deskManagerFormGroup.getRawValue(),
          dateOfBirth: this.datePipe.transform(this.deskManagerFormGroup.getRawValue().dateOfBirth, this.constants.dateFormats.yyyy_MM_dd),
          leaveBalances: this.getLeaveBalanceDto
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showBtnLoader = false;
          this.selectedDeskManagerDetails
            ? this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Desk Manager'))
            : this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'Desk Manager'));
          this.isInstructorAdded.emit();
          this.closeSideNavFun();
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavFun(): void {
    this.getInstructorFormArray('deskManagerAvailabilityAndLocations').clear();
    this.getInstructorFormArray('leaveBalances').clear();
    this.addNewLocationAndInstructorAvailability();
    this.addLeaveBalance(this.leaveObject(LeaveType.PAID));
    this.addLeaveBalance(this.leaveObject(LeaveType.UNPAID));
    this.closeSideNav.emit(this.selectedDeskManagerDetails);
    this.deskManagerFormGroup.reset();
  }

  asIsOrder() {
    return 1;
  }
}
