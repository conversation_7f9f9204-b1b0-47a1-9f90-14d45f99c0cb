<ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : dashboardContent"></ng-container>

<ng-template #dashboardContent>
  <div class="auth-page-wrapper">
    <div class="dashboard-header">
      <h1>{{ getTimeBasedGreeting() }}, {{ currentUser?.firstName }} {{ currentUser?.lastName }}</h1>
      <p class="dashboard-date">{{ today | date: constants.fullDayDate }}</p>
    </div>

    <div class="dashboard-card">
      <ng-container [ngTemplateOutlet]="currentUser?.userRoleId === constants.roleIds.CLIENT ? clientContent : adminContent"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #adminContent>
  <div class="row">
    <div class="col-md-6">
      <div class="row">
        <div [ngClass]="currentUser?.userRoleId === constants.roleIds.INSTRUCTOR ? 'col-md-12' : 'col-md-6'">
          <div class="o-card summary-card mb-0" (click)="navigateTo('/members/clients')" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER, constants.roles.SUPERVISOR, constants.roles.INSTRUCTOR]">
            <div class="summary-icon">
              <img [src]="constants.staticImages.icons.totalMembers" alt="Students" />
            </div>
            <div class="summary-content">
              <div class="summary-count">{{ dashboardData.studentDetails.totalNumberOfStudent }}</div>
              <div class="summary-title">Total Clients</div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="o-card summary-card" (click)="navigateTo('/members/instructors')" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER, constants.roles.SUPERVISOR]">
            <div class="summary-icon">
              <img [src]="constants.staticImages.icons.totalMembers" alt="Staff" />
            </div>
            <div class="summary-content">
              <div class="summary-count">{{ dashboardData.totalNumberOfStaffMember }}</div>
              <div class="summary-title">Total Staffs</div>
            </div>
          </div>
        </div>
      </div>

      <div class="o-card" *appHasPermission="[constants.roles.SUPERVISOR, constants.roles.INSTRUCTOR]">
        <div class="card-header">
          <div class="card-title">
            <img [src]="constants.staticImages.icons.timeCircleClock" alt="">
            Today's Working Hours
          </div>
        </div>
        <div class="card-content">
          <div class="d-flex justify-content-center">
            <div class="toggle-btn-wrapper ms-4 mb-3" *appHasPermission="[constants.roles.SUPERVISOR]">
              @for (scheduleOption of constants.scheduleOptions; track $index) {
              <div
                [ngClass]="{ active: showSupervisorDailyWorkingHours === scheduleOption.value }"
                class="select-btn"
                (click)="setDailyWorkingHoursData(scheduleOption.value)"
              >
                {{ scheduleOption.label.replace('Schedule', 'Working Hours') }}
              </div>
              }
            </div>
          </div>
            @if (!showSupervisorDailyWorkingHours) {
              <div class="leave-dashboard">
                <app-chart-viewer [chartOption]="dailyWorkingHoursChartOptions" [isWorkingHoursChart]="true"></app-chart-viewer>
                <div class="ms-4">
                    <div>
                        <div class="total-leaves">
                            {{ formatHoursToHM(totalDailyWorkingHours) }} <div class="content">Total Working Hours</div>
                        </div>
                        <div class="used-leaves mb-2">
                            <div class="dot used-dot"></div>Pending Hours: {{ formatHoursToHM(pendingDailyWorkingHours) || 0 }}
                        </div>
                        <div class="available-leaves">
                            <div class="dot available-dot"></div>Completed Hours: {{ formatHoursToHM(completedDailyWorkingHours) || 0 }}
                        </div>
                    </div>
                </div>
              </div>
            }
            @else {
              <div class="instructor-working-hours">
              @for (workingHours of dashboardData.instructorDailyWorkingHours; track $index) {
                  <div class="person-item">
                    <div class="person-avatar">
                      <img *ngIf="workingHours.profilePhoto" [src]="workingHours.profilePhoto" alt="Profile" />
                      <div *ngIf="!workingHours.profilePhoto" class="placeholder-name">
                        <div>
                          {{ getInitialsUsingFullName(workingHours.instructorName) | uppercase }}
                        </div>
                      </div>
                    </div>
                    <div class="person-details">
                      <div class="person-name">{{ workingHours.instructorName | titlecase }}</div>
                    </div>
                    <div class="me-3">
                      {{ formatHoursToHM(workingHours.totalWorkingHours) }}/{{ formatHoursToHM(workingHours.totalActualHours) }}
                    </div>
                  </div>
                  <div class="dotted-divider" *ngIf="!$last"></div>
                }
              </div>
            }
        </div>
      </div>

      <div class="o-card" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
          <div class="card-header">
            <div class="card-title">
              <img [src]="constants.staticImages.icons.cake" alt="cake">
              {{ todayBirthdays.length }} Birthdays
            </div>
          </div>
          <div class="card-content">
            @if (todayBirthdays.length) {
              <div class="upcoming-title">Birthdays Today</div>
              <div class="birthday-section">
                <div class="birthday-avatars">
                  <div class="birthday-avatar" *ngFor="let birthday of todayBirthdays">
                    <div matTooltip="{{ roleIds[birthday.roleId].replace('_', ' ') | titlecase }}">
                      <img *ngIf="birthday.profilePhoto" [src]="birthday.profilePhoto" alt="{{ birthday.name }}" />
                      <div *ngIf="!birthday.profilePhoto" class="placeholder-name m-0">
                        <div>
                          {{ getInitialsUsingFullName(birthday.name) | uppercase }}
                        </div>
                      </div>
                    </div>
                    <div class="birthday-name" [matTooltip]="birthday.name">{{ birthday.name | titlecase }}</div>
                  </div>
                </div>
              </div>
            }
            <div class="dotted-divider" *ngIf="todayBirthdays.length && upcomingBirthdays.length"></div>
            <div *ngIf="upcomingBirthdays.length" class="upcoming-birthdays">
              <div class="upcoming-title">Upcoming Birthdays</div>
              <div class="birthday-avatars">
                <div class="birthday-avatar" *ngFor="let birthday of upcomingBirthdays">
                  <div matTooltip="{{ roleIds[birthday.roleId].replace('_', ' ') | titlecase }}">
                    <img *ngIf="birthday.profilePhoto" [src]="birthday.profilePhoto" alt="{{ birthday.name }}" />
                    <div *ngIf="!birthday.profilePhoto" class="placeholder-name m-0">
                      <div>
                        {{ getInitialsUsingFullName(birthday.name) | uppercase }}
                      </div>
                    </div>
                  </div>
                  <div class="birthday-name" [matTooltip]="birthday.name">{{ birthday.name | titlecase }}</div>
                  <div class="birthday-date">{{ birthday.dateOfBirth | date: constants.dateFormats.d_MMM }}</div>
                </div>
              </div>
            </div>

            <app-empty-state
              *ngIf="!todayBirthdays.length && !upcomingBirthdays.length"
              [imageSrc]="constants.staticImages.icons.cake"
              title="No Birthdays"
              message="There are no birthdays today or in the upcoming week."
            ></app-empty-state>
          </div>
      </div>

      <div class="o-card" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER, constants.roles.SUPERVISOR, constants.roles.INSTRUCTOR]">
          <div class="card-header">
            <div class="card-title">
              <img [src]="constants.staticImages.icons.calendarIcon" alt="calendar">
              Today's Schedule
            </div>
            <div class="d-flex align-items-center">
              <div class="view-all" (click)="navigateTo('/schedule')">View All</div>
            </div>
          </div>
          <div class="card-content">
            <div class="d-flex justify-content-center">
              <div class="toggle-btn-wrapper ms-4" *appHasPermission="[constants.roles.SUPERVISOR]">
                @for (scheduleOption of constants.scheduleOptions; track $index) {
                <div
                  [ngClass]="{ active: showSupervisorSchedule === scheduleOption.value }"
                  class="select-btn"
                  (click)="setScheduleData(scheduleOption.value)"
                >
                  {{ scheduleOption.label }}
                </div>
                }
              </div>
            </div>
            <div class="timeline-container" [ngClass]="{ 'minHeight': scheduleLessonDetails && scheduleLessonDetails.length }">
              @if (scheduleLessonDetails.length) {
                @for (event of scheduleLessonDetails; track $index) {
                  <div class="timeline-item">
                    <div class="time">{{ event.start | date: constants.dateFormats.hh_mm_a }}</div>
                    <div
                      class="dot-with-line"
                      [ngStyle]="{ border: '4px solid ' + (event.instrumentFontColor || constants.colors.blueColor) }"
                      [ngClass]="{ 'no-line': $last }"></div>
                    <div class="details">
                      <div class="instrument-detail" [ngStyle]="{ color: event.instrumentFontColor || constants.colors.blueColor }">
                        <span [ngClass]="{ strike: event.isCancelSchedule }">
                          <span *ngIf="event.isCancelSchedule">Canceled: </span>
                          <span *ngIf="event.isDraftSchedule">Draft: </span>
                          @if (event.classType === classTypes.GROUP_CLASS) {
                            <span *ngIf="event.classType === classTypes.GROUP_CLASS" class="name">{{ event.groupClassName }}</span>
                          }@else if (event.classType === classTypes.ENSEMBLE_CLASS) {
                            <span class="name">{{ event.ensembleClassName }}</span>
                          }
                          @else {
                            <span class="name"
                              >{{ event.instrumentName ? event.instrumentName + " Lessons" : event.campName }}
                            </span>
                          }
                          <span class="duration">({{ getTimeDiff(event.start, event.end) }})</span>
                        </span>
                      </div>
                      <div class="additional-info">
                          <div matTooltip="Instructor" >
                            <img [src]="constants.staticImages.icons.memberIcon" alt="" />
                            <span
                              >{{ event.instructorName ?? event.assignedInstructors[0].instructorName | titlecase }}
                              @if (event.assignedInstructors.length > 1) {
                                <span class="remaining-instructor-available-count"> +{{ event.assignedInstructors.length - 1 }} </span>
                              }
                            </span>
                          </div>
                        @if (event.studentDetails.length) {
                          <div class="dot"></div>
                          <div matTooltip="Client">
                            <img [src]="constants.staticImages.icons.memberIcon" alt="" />
                            <span
                              >{{ event.studentDetails[0].studentName | titlecase }}
                              @if (event.studentDetails.length > 1) {
                                <span class="remaining-instructor-available-count"> +{{ event.studentDetails.length - 1 }} </span>
                              }
                            </span>
                          </div>
                        }
                      </div>
                    </div>
                  </div>
                }
              }
              @else {
                <app-empty-state
                  icon="event_available"
                  title="No Schedule today"
                  message="There are no scheduled lessons today."
                ></app-empty-state>
              }
            </div>
          </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="o-card" *appHasPermission="[constants.roles.SUPERVISOR, constants.roles.DESK_MANAGER, constants.roles.INSTRUCTOR]">
        <div class="card-header">
          <div class="card-title">
            <img [src]="constants.staticImages.icons.chartLineStar" alt="">
            Leave Balance
          </div>
          <div class="d-flex align-items-center">
            <div class="d-flex align-items-center">
              <div class="view-all me-3" (click)="navigateTo('/request/leave-request', true)">Request Leave</div>
              <div class="view-all" (click)="navigateTo('/request/leave-request')">View All</div>
            </div>
          </div>
        </div>
        <div class="card-content">
          <div class="leave-dashboard">
            <app-chart-viewer [chartOption]="chartOptions"></app-chart-viewer>
            <div class="ms-4">
                <div>
                    <div class="total-leaves">
                        {{ totalLeaves }} <div class="content">Total Leaves</div>
                    </div>
                    <div class="used-leaves mb-2">
                        <div class="dot used-dot"></div> Used Leaves: {{ usedLeaves || 0 }}
                    </div>
                    <div class="available-leaves">
                        <div class="dot available-dot"></div> Available Leaves: {{ availableLeaves || 0 }}
                    </div>
                </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="o-card" *appHasPermission="[constants.roles.SUPERVISOR, constants.roles.INSTRUCTOR]">
        <div class="card-header">
          <div class="card-title">
            <img [src]="constants.staticImages.icons.timeCircleClock" alt="">
            Month's Working Hours
          </div>
        </div>
        <div class="card-content">
          <div class="d-flex justify-content-center">
            <div class="toggle-btn-wrapper ms-4 mb-3" *appHasPermission="[constants.roles.SUPERVISOR]">
              @for (scheduleOption of constants.scheduleOptions; track $index) {
              <div
                [ngClass]="{ active: showSupervisorMonthlyWorkingHours === scheduleOption.value }"
                class="select-btn"
                (click)="setMonthlyWorkingHoursData(scheduleOption.value)"
              >
                {{ scheduleOption.label.replace('Schedule', 'Working Hours') }}
              </div>
              }
            </div>
          </div>
            @if (!showSupervisorMonthlyWorkingHours) {
              <div class="leave-dashboard">
                <app-chart-viewer [chartOption]="monthlyWorkingHoursChartOptions" [isWorkingHoursChart]="true"></app-chart-viewer>
                <div class="ms-4">
                    <div>
                        <div class="total-leaves">
                            {{ formatHoursToHM(totalMonthlyWorkingHours) }} <div class="content">Total Working Hours</div>
                        </div>
                        <div class="used-leaves mb-2">
                            <div class="dot used-dot"></div>Pending Hours: {{ formatHoursToHM(pendingMonthlyWorkingHours) || 0 }}
                        </div>
                        <div class="available-leaves">
                            <div class="dot available-dot"></div>Completed Hours: {{ formatHoursToHM(completedMonthlyWorkingHours) || 0 }}
                        </div>
                    </div>
                </div>
              </div>
            }
            @else {
              <div class="instructor-working-hours">
              @for (workingHours of dashboardData.instructorMonthlyWorkingHours; track $index) {
                  <div class="person-item">
                    <div class="person-avatar">
                      <img *ngIf="workingHours.profilePhoto" [src]="workingHours.profilePhoto" alt="Profile" />
                      <div *ngIf="!workingHours.profilePhoto" class="placeholder-name">
                        <div>
                          {{ getInitialsUsingFullName(workingHours.instructorName) | uppercase }}
                        </div>
                      </div>
                    </div>
                    <div class="person-details">
                      <div class="person-name">{{ workingHours.instructorName | titlecase }}</div>
                    </div>
                    <div class="me-3">
                      {{ formatHoursToHM(workingHours.totalWorkingHours) }}/{{ formatHoursToHM(workingHours.totalActualHours) }}
                    </div>
                  </div>
                  <div class="dotted-divider" *ngIf="!$last"></div>
                }
              </div>
            }
        </div>
      </div>

      <div class="o-card" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER, constants.roles.SUPERVISOR]">
        <div class="card-header" (click)="isOnLeaveCardCollapsed = !isOnLeaveCardCollapsed">
          <div class="card-title">
            <img [src]="constants.staticImages.icons.chartLineStar" alt="">
            Today On Leave
          </div>
          <div class="d-flex align-items-center">
            @if (dashboardData.memberOnLeave && dashboardData.memberOnLeave.length > constants.maxTwoVisibileItems) {
              <div class="view-all" (click)="showAllOnLeave = !showAllOnLeave">
                {{ showAllOnLeave ? 'Show Less' : 'Show All' }}
              </div>
            }
          </div>
        </div>
        <div class="card-content">
          @for (leave of dashboardData.memberOnLeave; track $index) {
            @if ($index < constants.maxTwoVisibileItems || showAllOnLeave) {
              <div class="person-item">
                <div class="person-avatar">
                  <img *ngIf="leave.profilePhoto" [src]="leave.profilePhoto" alt="Profile" />
                  <div *ngIf="!leave.profilePhoto" class="placeholder-name">
                    <div>
                      {{ getInitialsUsingFullName("Octopus Instructor") | uppercase }}
                    </div>
                  </div>
                </div>
                <div class="person-details">
                  <div class="person-name">{{ "Octopus Instructor" | titlecase }}</div>
                  <div class="person-role">Substitute: {{ leave.substituteName || 'None assigned' }}</div>
                </div>
                <div class="role">
                  {{ leave.role }}
                </div>
              </div>
              @if (shouldShowDivider(dashboardData.memberOnLeave.length, $index, showAllOnLeave, constants.maxTwoVisibileItems)) {
                <div class="dotted-divider"></div>
              }
            }
          }
          <app-empty-state
            *ngIf="!dashboardData.memberOnLeave.length"
            icon="event_available"
            title="Everyone is working today!"
            message="There are no members on leave today."
          ></app-empty-state>
        </div>
      </div>
      <div class="o-card" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <div class="card-header">
          <div class="card-title">
            <img [src]="constants.staticImages.icons.payment" alt="Payment" />
            Pending Payments
          </div>
          <div class="d-flex align-items-center">
            <div class="view-all" (click)="navigateTo('/pending-payments')">View All</div>
          </div>
        </div>
        <div class="card-content">
          @for (pendingPayment of pendingPayments; track $index) {
            @if ($index < 2) {
              <div class="person-item pointer" (click)="navigateToStudentViewPage(pendingPayment.userBillingTransactions.dependentInformationId)">
                <div class="person-avatar">
                  <div class="placeholder-name">
                    <div>
                      {{ getInitialsUsingFullName(pendingPayment.userBillingTransactions.accountManagerName) | uppercase }}
                    </div>
                  </div>
                </div>
                <div class="person-details">
                  <div class="person-name">{{ pendingPayment.userBillingTransactions.accountManagerName | titlecase }}</div>
                  <div class="person-role">Amount <span class="text-black">{{ pendingPayment.userBillingTransactions.billAmount | currency : 'USD' : true : '1.2-2' }}</span> </div>
                </div>
                <div class="enrollment-status notEnrolled">
                  Due Date {{ pendingPayment.userBillingTransactions.billDate | date: constants.dateFormats.MMM_D_Y }}
                </div>
              </div>
              <div class="dotted-divider" *ngIf="pendingPayments.length > 1 && $first"></div>
            }
          }
          <app-empty-state
            *ngIf="!pendingPayments.length"
            icon="payment"
            title="No Pending Payments"
            message="There are no pending payments at this time."
          ></app-empty-state>
        </div>
      </div>
      <div class="o-card" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER, constants.roles.SUPERVISOR]">
        <div class="card-header">
          <div class="card-title">
            <img [src]="constants.staticImages.icons.checkedFile" alt="Request" />
            Pending Leave Requests
          </div>
          <div class="d-flex align-items-center">
            <div class="view-all" (click)="navigateToLeaveRequest()">View All</div>
          </div>
        </div>
        <div class="card-content">
          @for (request of dashboardData.leaveRequest; track $index) {
            @if ($index < 2) {
              <div class="person-item">
                <div class="person-avatar">
                  <img *ngIf="request.profilePhoto" [src]="request.profilePhoto" alt="Profile" />
                  <div *ngIf="!request.profilePhoto" class="placeholder-name">
                    <div>
                      {{ getInitialsUsingFullName(request.requestedBy) | uppercase }}
                    </div>
                  </div>
                </div>
                <div class="person-details">
                  <div class="person-name">{{ request.requestedBy | titlecase }}</div>
                  <div class="person-role">{{ request.role }}</div>
                </div>
                <div class="enrollment-status achPending">
                  Requested on {{ request.requestDate | date: constants.dateFormats.MMM_D_Y }}
                </div>
              </div>
              <div class="dotted-divider" *ngIf="dashboardData.leaveRequest.length > 1 && $first"></div>
            }
          }
          <app-empty-state
            *ngIf="!dashboardData.leaveRequest.length"
            icon="event_available"
            title="No Leave Requests"
            message="There are no pending leave requests at this time."
          ></app-empty-state>
        </div>
      </div>
      <div class="o-card" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
          <div class="card-header">
            <div class="card-title">
              <img [src]="constants.staticImages.icons.crossCircle" alt="">
              Quit Plan Requests
            </div>
            <div class="d-flex align-items-center">
              <div class="view-all" (click)="navigateTo('/request/plan-cancel-request')">View All</div>
            </div>
          </div>
          <div class="card-content">
            @for (request of dashboardData.studentDiscontinuedPlans; track $index) {
              @if ($index < 2) {
                <div class="person-item pointer" (click)="navigateToStudentViewPage(request.studentplan.dependentInformationId)">
                  <div class="person-avatar">
                    <img *ngIf="request.profilePhoto" [src]="request.profilePhoto" alt="Profile" />
                    <div *ngIf="!request.profilePhoto" class="placeholder-name">
                      <div>
                        {{ getInitialsUsingFullName(request.studentDiscontinuedPlanDetails?.requestedBy ?? '') | uppercase }}
                      </div>
                    </div>
                  </div>
                  <div class="person-details">
                    <div class="person-name">{{ request.studentDiscontinuedPlanDetails?.requestedBy | titlecase }}</div>
                    <div class="person-role">
                      Weekly {{ request.studentDiscontinuedPlanDetails?.isEnsembleAvailable ? "Ensemble" : request?.instrumentName }} Lessons ({{ request.planDetails[0].planDetail.duration }})
                    </div>
                  </div>
                  <div class="enrollment-status" [ngClass]="getStatusClass(request.studentDiscontinuedPlanDetails?.planCancelRequestStatus)">
                    {{ planCancelStatus[request.studentDiscontinuedPlanDetails!.planCancelRequestStatus] | titlecase }} {{ request.studentDiscontinuedPlanDetails?.requestDate | date: constants.dateFormats.MMM_D_Y }}
                  </div>
                </div>
                <div class="dotted-divider" *ngIf="dashboardData.studentDiscontinuedPlans.length > 1 && $first"></div>
              }
            }
            <app-empty-state
              *ngIf="!dashboardData.studentDiscontinuedPlans.length"
              icon="check_circle"
              title="No Quit Plan Requests"
              message="There are no students requesting to quit their plans at this time."
            ></app-empty-state>
          </div>
      </div>
      <div class="o-card" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <div class="card-header">
          <div class="card-title">
            <img [src]="constants.staticImages.icons.checkedFile" alt="Request" />
            Maintenance Requests
          </div>
          <div class="d-flex align-items-center">
            <div class="view-all" (click)="navigateTo('/request/maintenance-request')">View All</div>
          </div>
        </div>
        <div class="card-content">
          @for (request of dashboardData.dashboardMaintenanceRequest; track $index) {
            @if ($index < 2) {
              <div class="person-item">
                <div class="person-avatar">
                  <img *ngIf="request.profilePhoto" [src]="request.profilePhoto" alt="Profile" />
                  <div *ngIf="!request.profilePhoto" class="placeholder-name">
                    <div>
                      {{ getInitialsUsingFullName(request.createdByName) | uppercase }}
                    </div>
                  </div>
                </div>
                <div class="person-details">
                  <div class="person-name">{{ request.createdByName | titlecase }}</div>
                  <div class="person-role">
                    <img [src]="constants.staticImages.icons.location" alt="">
                    {{ request.locationName }}
                  </div>
                </div>
                <div class="enrollment-status" [ngClass]="{
                  enrolled: request.status === maintenanceRequestStatus.Closed,
                  notEnrolled: request.status === maintenanceRequestStatus.OnHold,
                  achPending: request.status === maintenanceRequestStatus.Open,
                  resolved: request.status === maintenanceRequestStatus.InProgress,
                }">
                  {{ maintenanceRequestStatus[request.status] | titlecase }} {{ request.createdOn | date: constants.dateFormats.MMM_D_Y }}
                </div>
              </div>
              <div class="dotted-divider" *ngIf="dashboardData.dashboardMaintenanceRequest.length > 1 &&  $first"></div>
            }
          }
          <app-empty-state
            *ngIf="!dashboardData.dashboardMaintenanceRequest.length"
            icon="build"
            title="No Maintenance Requests"
            message="All equipments are working properly."
          ></app-empty-state>
        </div>
      </div>
      <div class="o-card" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <div class="card-header">
          <div class="card-title">
            <img [src]="constants.staticImages.icons.studentEnquiryIcon" alt="Inquiry" class="black" />
            Client Inquiry
          </div>
          <div class="d-flex align-items-center">
            <div class="view-all" (click)="navigateTo('/client-inquiry')">View All</div>
          </div>
        </div>
        <div class="card-content">
          @for (inquiry of dashboardData.inquiryDetails; track $index) {
            @if ($index < 2) {
            <div class="person-item">
                <div class="person-avatar">
                  <div class="placeholder-name">
                    <div>
                      {{ getInitials(inquiry.firstName, inquiry.lastName) | uppercase }}
                    </div>
                  </div>
                </div>
                <div class="person-details">
                  <div class="person-name">{{ inquiry.firstName | titlecase }} {{ inquiry.lastName | titlecase }}</div>
                  <div class="person-role">
                    <img [src]="constants.staticImages.icons.location" alt="">
                    {{ inquiry.locationName }}
                  </div>
                </div>
                <div class="enrollment-status" [ngClass]="inquiry.enrolledDate ? 'enrolled' : 'notEnrolled'">
                  {{ inquiry.enrolledDate ? 'Enrolled ' + (inquiry.enrolledDate | date:'MMM dd, yyyy') : 'Not Enrolled' }}
                </div>
            </div>
            <div class="dotted-divider" *ngIf="dashboardData.inquiryDetails.length > 1 && $first"></div>
            }
          }
          <app-empty-state
            *ngIf="!dashboardData.inquiryDetails.length"
            icon="contact_support"
            title="No Student Inquiries"
            message="There are no new student inquiries at this time."
          ></app-empty-state>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #clientContent>
  <app-client-dashboard [dashboardData]="dashboardData"></app-client-dashboard>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>