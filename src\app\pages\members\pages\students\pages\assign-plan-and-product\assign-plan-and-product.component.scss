@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.o-sidebar-wrapper {
  .back-btn-wrapper {
    @include flex-content-align-start;

    .name {
      @include flex-content-align-center;
    }
  }

  .action-btn-wrapper {
    @include flex-content-align-center;

    .cart-wrapper {
      position: relative;
      cursor: pointer;

      .cart-count {
        @include flex-content-center;
        position: absolute;
        top: 2px;
        right: 13px;
        height: 20px;
        width: 20px;
        border-radius: 50%;
        background-color: $primary-color;
        color: $white-color;
        font-size: 11px;
      }
    }
  }

  .o-sidebar-body {
    padding: 20px 30px !important;
    overflow: auto;
    height: calc(100vh - 85px);

    .select-assign-items {
      .title {
        font-size: 16px;
        font-weight: 700;
        color: $gray-text;
        margin-bottom: 6px;
      }
    }

    .accordion-wrapper {
      .accordion-header {
        @include flex-content-space-between;
        padding: 10px 0px;
        border-bottom: 1px solid $dot-color;

        .accordion-title {
          font-size: 17px;
          font-weight: 700;
          color: $primary-color;
        }

        .accordion-icon {
          cursor: pointer;
        }

        .accordion-header-btn {
          @include flex-content-align-center;
        }
      }

      .accordion-content {
        .accordion-item {
          @include flex-content-space-between;
          margin-top: 15px;
          border-radius: 10px;
          padding: 12px 18px;
          background-color: $gray-bg-light;

          .plan-name {
            font-weight: 700;
            font-size: 17px;
            margin-bottom: 5px;
          }

          .filters-and-content {
            @include flex-content-align-center;
          }

          .plan-content {
            @include flex-content-align-center;
            font-size: 15px;
            font-weight: 600;
          }

          .plan-pricing {
            font-size: 16px;
            font-weight: 600;
            text-align: end;

            &.assigned-plan {
              gap: 15px;
              @include flex-content-align-center;
            }

            button {
              height: 35px !important;
              width: 95px !important;
              padding: 0 !important;
            }

            .plan-cancel-btn {
              @include flex-content-center;
              cursor: pointer;

              .cross-circle {
                filter: $red-filter;
                height: 26px;
                width: 26px;
              }
            }

            ::ng-deep {
              .mat-primary-btn .mat-mdc-focus-indicator {
                height: 35px !important;
              }

              .mdc-button__label {
                font-size: 12px !important;
              }
            }
          }
        }
      }

      .accordion-filters {
        display: flex;
        height: 35px;

        .search-bar-wrapper {
          margin: 0px 5px;
        }

        ::ng-deep {
          .mat-mdc-select-value-text {
            font-size: 14px;
          }
          .mat-mdc-form-field-flex {
            height: 35px !important;
          }
          .mdc-text-field {
            background-color: $white-color !important;
            border: 1px solid $btn-options-border-color;
            width: 205px;
          }
        }
      }

      .other-plan {
        height: 30px !important;
        margin-bottom: 5px;

        ::ng-deep {
          .mdc-text-field {
            width: 160px !important;
          }
          .mat-mdc-form-field-flex {
            height: 30px !important;
          }
        }
      }
    }
  }
}

@media (max-width: 767px) {
  .accordion-content {
    .accordion-item,
    .filters-and-content,
    .plan-content {
      flex-wrap: wrap;
    }
  }

  .filters-and-content {
    .dot {
      display: none;
    }
  }

  .accordion-filters {
    display: none !important;
  }
  .other-plan {
    display: flex !important;
  }
}

::ng-deep {
  .mat-drawer-inner-container {
    overflow: hidden;
  }
}
