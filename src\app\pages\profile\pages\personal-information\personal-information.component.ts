import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AbstractControl, AbstractControlOptions, FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { Account, State, UserFormGroupType } from 'src/app/auth/models/user.model';
import { AuthService } from 'src/app/auth/services';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { ProfileService } from '../../services/profile.service';
import { DependentInfo, DependentInfoForm, SignUpForOptions } from 'src/app/auth/models';
import { CBGetResponse, CBResponse, FileUpload, MatDialogRes } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { UploadFileService } from 'src/app/shared/services/upload-file.service';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { DependentService } from '../../services/dependent.service';
import { RoomDetails, RoomInstrumentList, SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { InstructorInstrument } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import {
  AvailabilityType,
  GradeLevel,
  InstructorAvaibilityInInstructorDetail,
  InstructorAvailabilityFormGroup,
  InstructorInstrumentFormGroup,
  LabelValueKey,
  LeaveBalanceFormGroup
} from 'src/app/pages/members/pages/instructors/models';
import { Instrument } from 'src/app/request-information/models';
import { RoomService } from 'src/app/pages/room-and-location-management/pages/room/services';
import { DatePipe } from '@angular/common';
import moment from 'moment';
import {
  dateOfBirthValidator,
  outOfRangeTimeValidator,
  phoneNumberValidator,
  timeRangeValidator,
  zipCodeValidator
} from 'src/app/shared/validators';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { DeskManagerDetailsService } from 'src/app/pages/members/pages/desk-managers/services';
import { LeaveRequestService } from 'src/app/pages/requests/pages/leave-request/services';
import { LeaveBalance, LeaveType, MemberDayViseLeave } from 'src/app/pages/requests/pages/leave-request/models';

@Component({
  selector: 'app-personal-information',
  templateUrl: './personal-information.component.html',
  styleUrls: ['./personal-information.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PersonalInformationComponent extends BaseComponent implements OnInit {
  isInstructor!: boolean;
  instrumentTypes!: Array<Instrument>;
  gradeLevels!: Array<GradeLevel>;
  states!: Array<State>;
  rooms!: Array<RoomDetails>;
  locations!: Array<SchoolLocations>;
  userFormGroup!: FormGroup<UserFormGroupType>;
  showImageLoader!: boolean;
  isDeskManager!: boolean;
  isChild!: boolean | undefined;
  deleteChildIds: number[] = [];
  maxDate = new Date();
  availabilityTypes = AvailabilityType;
  leaveData!: MemberDayViseLeave[];

  @Output() formSubmittedSuccess = new EventEmitter<void>();

  constructor(
    private readonly authService: AuthService,
    private readonly profileService: ProfileService,
    private readonly toasterService: AppToasterService,
    private readonly uploadFileService: UploadFileService,
    private readonly cdr: ChangeDetectorRef,
    private readonly dialog: MatDialog,
    private readonly dependentService: DependentService,
    private readonly commonService: CommonService,
    private readonly roomService: RoomService,
    private readonly datePipe: DatePipe,
    private readonly instructorService: InstructorService,
    private readonly deskManagerDetailsService: DeskManagerDetailsService,
    private readonly leaveRequestService: LeaveRequestService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeUserForm();
    this.getAllLocations();
    this.getInstruments();
    this.getGradeLevels();
    this.getAllStates();
    this.getCurrentUser();
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser(true)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          if (this.currentUser?.isDependent) {
            this.isChild = this.currentUser.dependentDetails[0]?.userType === SignUpForOptions.YOUR_CHILD;
          }
          this.isInstructor = this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR;
          this.isDeskManager = this.currentUser?.userRoleId === this.constants.roleIds.DESK_MANAGER;
          this.setCurrentUserForm();
          this.setRequiredBasedOnCondition('lastName', !this.isInstructor && !this.currentUser?.isSupervisor && !this.isDeskManager);
          this.setDependentInfo();
          this.setInstructorForm();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getAllLocations(): void {
    this.authService
      .getAllLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instrumentTypes = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getAllStates(): void {
    this.commonService
      .getStates()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Array<State>>) => {
          this.states = res.result;
          this.cdr.detectChanges();
        }
      });
  }

  getRooms(i: number): void {
    this.getInstructorFormArray('instructorAvailability').controls[i].get('roomId')?.reset();
    this.roomService
      .add({ page: 1 }, API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RoomDetails>) => {
          this.rooms = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getRoomsByLocation(i: number): RoomDetails[] {
    return this.rooms?.filter(
      room => room.roomDetail.locationId === this.getInstructorFormArray('instructorAvailability').controls[i]?.get('locationId')?.value
    );
  }

  getRoomInstrumentNames(i: number): RoomInstrumentList[] | undefined {
    const selectedRoomId = this.getInstructorFormArray('instructorAvailability').controls[i]?.get('roomId')?.value;
    return this.rooms?.find(room => room.roomDetail.id === selectedRoomId)?.roomDetail.roomInstrumentList;
  }

  getGradeLevels(): void {
    this.gradeLevels = [];
    for (let i = 1; i <= 10; i++) {
      this.gradeLevels.push({ gradeLevel: { id: i, name: String(i) } });
    }
  }

  getMinStartDate(index: number, controlName: string): Date {
    const availabilityControl = this.getInstructorFormArray(controlName).at(index);
    const availableStartDate = availabilityControl.get('availableStartDate')?.value;

    const startDate = availableStartDate ? new Date(availableStartDate) : this.maxDate;
    return startDate > this.maxDate ? this.maxDate : startDate;
  }

  setCurrentUserForm(): void {
    this.userFormGroup.patchValue({
      firstName: this.currentUser?.firstName,
      lastName: this.currentUser?.lastName,
      emailAddress: this.currentUser?.emailAddress,
      phoneNumber: this.currentUser?.phoneNumber,
      dateOfBirth: this.currentUser?.dateOfBirth,
      address: this.currentUser?.address,
      stateId: this.currentUser?.stateId,
      city: this.currentUser?.city,
      zipCode: this.currentUser?.zipCode,
      profilePicture: this.currentUser?.profilePicture,
      profilePicturefullurl: this.currentUser?.profilePicturefullurl,
      bio: this.currentUser?.bio
    });
    this.cdr.detectChanges();
  }

  setInstructorForm(): void {
    if (this.isInstructor || this.currentUser?.isSupervisor) {
      this.setInstruments(this.currentUser?.instruments);
      this.setLocationAndAvailability(this.currentUser?.instructorAvailability);
    }
    if (this.isDeskManager) {
      this.setDeskManagerLocationAndAvailability(this.currentUser?.deskManagerAvailabilityAndLocations);
    }
    this.getLeaveBalance(this.currentUser?.emailAddress ?? '');
  }

  getLeaveBalance(email: string): void {
    this.leaveRequestService
      .getList<CBGetResponse<LeaveBalance[]>>(`${API_URL.leaveManagement.getLeaveBalance}?UserEmail=${email}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<LeaveBalance[]>) => {
          this.leaveData = res.result[0].memberDayViseLeaves;
          this.cdr.detectChanges();
        }
      });
  }

  showLeaveBalance(): boolean {
    const controlName = this.isDeskManager ? 'deskManagerAvailabilityAndLocations' : 'instructorAvailability';
    return this.getInstructorFormArray(controlName).controls.some(
      ctrl =>
        ctrl.get('availabilityType')?.value === this.availabilityTypes.DAILY ||
        (ctrl.get('availabilityType')?.value === this.availabilityTypes.WEEKLY && ctrl.get('availableDays')?.value.length)
    );
  }

  getLeaveData(day: number): any {
    return this.leaveData.find(item => item.day === day);
  }

  getDaysForTimeOff(): Array<LabelValueKey> {
    const controlName = this.isDeskManager ? 'deskManagerAvailabilityAndLocations' : 'instructorAvailability';
    const availabilities = this.getInstructorFormArray(controlName).controls;
    const hasDaily = availabilities.some(ctrl => ctrl.get('availabilityType')?.value === this.availabilityTypes.DAILY);
    if (hasDaily) {
      this.ensureLeaveDataForDays(this.constants.daysOfTheWeek);
      return this.constants.daysOfTheWeek;
    }

    const allSelectedDays = new Set<number>();
    availabilities.forEach(ctrl => {
      const days: number[] = ctrl.get('availableDays')?.value || [];
      days.forEach(day => allSelectedDays.add(day));
    });
    this.ensureLeaveDataForDays(Array.from(allSelectedDays).map(day => ({ value: day })));
    return this.constants.daysOfTheWeek.filter(day => allSelectedDays.has(day.value));
  }

  ensureLeaveDataForDays(days: Array<{ value: number }>): void {
    if (!Array.isArray(this.leaveData)) {
      this.leaveData = [];
    }
    days.forEach(dayObj => {
      const exists = this.leaveData.some(ld => ld.day === dayObj.value);
      if (!exists) {
        this.leaveData.push({
          day: dayObj.value,
          usedLeaveDays: 0,
          remainingLeaveDays: 0,
          totalLeaveDays: 0
        });
      }
    });
    this.leaveData = this.leaveData.filter(ld => days.some(dayObj => dayObj.value === ld.day));
  }

  isUserUnderAge(dob: string): boolean {
    const birthDate = moment(dob);
    const age = moment().diff(birthDate, 'years');
    return age < 18;
  }

  setInstruments(instruments: InstructorInstrument[] | undefined): void {
    const instrumentFormArray = this.getInstructorFormArray('instructorInstrument');
    instrumentFormArray.clear();
    instruments?.forEach(instrument => {
      this.addNewInstruments(instrument);
      const lastIndex = instrumentFormArray.length - 1;
      instrumentFormArray.at(lastIndex).get('instrumentId')?.disable();
    });
  }

  setLocationAndAvailability(instructorAvailability: InstructorAvaibilityInInstructorDetail[] | undefined): void {
    const availabilityFormArray = this.getInstructorFormArray('instructorAvailability');
    availabilityFormArray.clear();
    instructorAvailability?.forEach((availability, i) => {
      this.addNewLocationAndInstructorAvailability(availability);
      this.getRooms(i);
      this.getRoomsByLocation(i);
      availability.availableDays?.forEach(day => this.setDaysOfWeek(day, i));
      const lastIndex = availabilityFormArray.length - 1;
      availabilityFormArray.at(lastIndex).get('locationId')?.disable();
    });
    this.showPageLoader = false;
  }

  setDeskManagerLocationAndAvailability(instructorAvailability: InstructorAvaibilityInInstructorDetail[] | undefined): void {
    const availabilityFormArray = this.getInstructorFormArray('deskManagerAvailabilityAndLocations');
    availabilityFormArray.clear();
    instructorAvailability?.forEach((availability, i) => {
      this.addNewLocationAndInstructorAvailability(availability);
      availability.availableDays?.forEach(day => this.setDaysOfWeek(day, i));
      const lastIndex = availabilityFormArray.length - 1;
      availabilityFormArray.at(lastIndex).get('locationsId')?.disable();
    });
    this.showPageLoader = false;
  }

  resetValues(index: number, controlName: string): void {
    this.getInstructorFormArray(controlName).at(index)?.get('availabilityType')?.setValue(null);
    this.getInstructorFormArray(controlName).at(index)?.get('availableEndDate')?.setValue(null);
    this.getInstructorFormArray(controlName).at(index)?.get('neverEnd')?.setValue(null);
    (this.getInstructorFormArray(controlName).at(index)?.get('availableDays') as FormArray).clear();
  }

  setTime(control: string, i: number): void {
    const controlName = this.isDeskManager ? 'deskManagerAvailabilityAndLocations' : 'instructorAvailability';
    const availability = this.getInstructorFormArray(controlName).controls[i]?.get(control);
    const availabilityArray = this.isDeskManager
      ? this.currentUser?.deskManagerAvailabilityAndLocations
      : this.currentUser?.instructorAvailability;
    const currentDate = this.datePipe.transform(
      availabilityArray![i]?.availableStartDate ? availabilityArray![i]?.availableStartDate : this.maxDate,
      this.constants.dateFormats.yyyy_MM_dd
    );

    const timeValue = this.datePipe.transform(
      new Date(`${currentDate} ${availability?.value}`),
      this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
    );
    availability?.setValue(timeValue ?? '');
  }

  setDate(control: string, i: number): void {
    const controlName = this.isDeskManager ? 'deskManagerAvailabilityAndLocations' : 'instructorAvailability';
    this.setFormControlValue(
      control,
      this.datePipe.transform(
        this.getInstructorFormArray(controlName).controls[i]?.get(control)?.value,
        this.constants.dateFormats.yyyy_MM_dd
      )!,
      i
    );
  }

  setFormControlValue(control: string, value: number | string | boolean | Date, i: number): void {
    const controlName = this.isDeskManager ? 'deskManagerAvailabilityAndLocations' : 'instructorAvailability';
    const instructorForm = this.getInstructorFormArray(controlName).controls[i];
    instructorForm?.get(control)?.setValue(value);
    if (control === 'availabilityType') {
      this.setAvailabilityType(value, i);
    }
    if (control === 'availableEndDate') {
      instructorForm?.get('neverEnd')?.setValue(value === this.getOneYearLaterDate(instructorForm?.get('availableStartDate')?.value));
    }
  }

  getOneYearLaterDate(startDate?: string): string {
    return moment(startDate).add(1, 'year').format(this.constants.dateFormats.yyyy_MM_DD);
  }

  setAvailabilityType(value: number | string | boolean | Date, i: number): void {
    const controlName = this.isDeskManager ? 'deskManagerAvailabilityAndLocations' : 'instructorAvailability';
    const availability = this.getInstructorFormArray(controlName).controls[i];
    if (value === this.availabilityTypes.DAILY || value === this.availabilityTypes.WEEKLY) {
      this.setRequiredBasedOnCondition('availableEndDate', true, i);
    }
    if (value === this.availabilityTypes.WEEKLY) {
      this.setRequiredBasedOnCondition('availableDays', true, i);
    }
    if (value === this.availabilityTypes.DAILY) {
      this.setRequiredBasedOnCondition('availableDays', false, i);
      const availableDaysArray = availability.get('availableDays') as FormArray;
      availableDaysArray.clear();
      this.constants.daysOfTheWeek.forEach(day =>
        availableDaysArray.push(new FormControl(day.value, { nonNullable: true }))
      );
    }
    if (value === this.availabilityTypes.NO_REPEATS) {
      this.setRequiredBasedOnCondition('availableDays', false, i);
      availability.get('availableEndDate')?.setValue(availability.get('availableStartDate')?.value);
      (availability.get('availableDays') as FormArray).clear();
    }
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean, i?: number): void {
    const name = this.isDeskManager ? 'deskManagerAvailabilityAndLocations' : 'instructorAvailability';
    const control = i !== undefined ? this.getInstructorFormArray(name).controls[i]?.get(controlName) : this.userFormGroup.get(controlName);

    if (control) {
      if (required) {
        const validators = [Validators.required];
        if (controlName === 'lastName') {
          validators.push(Validators.pattern(this.constants.pattern.NAME_PATTERN));
        }
        control.setValidators(validators);
      } else {
        control.clearValidators();
      }
      control.updateValueAndValidity();
    }
  }

  setDaysOfWeek(dayValue: number, i: number): void {
    const name = this.isDeskManager ? 'deskManagerAvailabilityAndLocations' : 'instructorAvailability';
    const daysOfSchedule = this.getInstructorFormArray(name).controls[i].get('availableDays') as FormArray;
    const index = daysOfSchedule.value.indexOf(dayValue);

    if (index !== -1) {
      daysOfSchedule.removeAt(index);
    } else {
      daysOfSchedule.push(new FormControl(dayValue, { nonNullable: true }));
    }
  }

  isDaySelected(dayValue: number, i: number): boolean | undefined {
    const name = this.isDeskManager ? 'deskManagerAvailabilityAndLocations' : 'instructorAvailability';
    const daysOfSchedule = this.getInstructorFormArray(name).controls[i].get('availableDays')?.value;
    return daysOfSchedule?.includes(dayValue);
  }

  setDependentInfo(): void {
    if (this.currentUser?.dependentDetails?.length) {
      this.getDependentInfoFormArray.clear();
      for (const dependentInfo of this.currentUser?.dependentDetails) {
        this.addMoreChild(dependentInfo);
      }
    }
  }

  addMoreChild(info?: DependentInfo, addNew = false): void {
    let setDefaultInfo;
    if (addNew) {
      setDefaultInfo = this.currentUser?.dependentDetails[this.currentUser.dependentDetails.length - 1];
    }
    this.getDependentInfoFormArray.push(
      new FormGroup({
        firstName: new FormControl(info?.firstName ?? '', {
          nonNullable: true,
          validators: [Validators.required, Validators.pattern(this.constants.pattern.NAME_PATTERN)]
        }),
        lastName: new FormControl(info?.lastName ?? '', {
          nonNullable: true,
          validators: [Validators.required, Validators.pattern(this.constants.pattern.NAME_PATTERN)]
        }),
        dateOfBirth: new FormControl(info?.dateOfBirth ?? '', { nonNullable: true, validators: [Validators.required] }),
        locationId: new FormControl(info?.locationId ?? undefined, {
          nonNullable: true,
          validators: [Validators.required]
        }),
        id: new FormControl(info?.id ?? undefined, { nonNullable: true }),
        userType: new FormControl(addNew ? setDefaultInfo?.userType : info?.userType ?? undefined, {
          nonNullable: true
        }),
        mentorId: new FormControl(addNew ? setDefaultInfo?.mentorId : info?.mentorId ?? undefined, {
          nonNullable: true
        })
      })
    );
  }

  get getDependentInfoFormArray(): FormArray {
    return this.userFormGroup.get('dependentDetails') as FormArray;
  }

  onSubmit(): void {
    switch (this.currentUser?.userRoleId) {
      case this.constants.roleIds.INSTRUCTOR:
        return this.updateInstructorOrSupervisor();
      case this.constants.roleIds.SUPERVISOR:
        return this.updateInstructorOrSupervisor();
      case this.constants.roleIds.DESK_MANAGER:
        return this.updateDeskManager();
      default:
        return this.updateStudentProfile();
    }
  }

  get getLeaveBalanceDto(): LeaveBalance[] {
    if (this.showLeaveBalance()) {
      return [
        {
          leaveType: LeaveType.PAID,
          totalLeaveDays: this.leaveData.reduce((acc, item) => acc + item.totalLeaveDays, 0),
          usedLeaveDays: this.leaveData.reduce((acc, item) => acc + item.usedLeaveDays, 0),
          remainingLeaveDays: this.leaveData.reduce((acc, item) => acc + item.remainingLeaveDays, 0),
          memberDayViseLeaves: this.leaveData.map(item => ({
            day: item.day,
            usedLeaveDays: item.usedLeaveDays,
            remainingLeaveDays: item.remainingLeaveDays,
            totalLeaveDays: item.totalLeaveDays
          }))
        }
      ];
    }
    return [
        {
          leaveType: LeaveType.PAID,
          totalLeaveDays: 0,
          usedLeaveDays: 0,
          remainingLeaveDays: 0,
          memberDayViseLeaves: []
        }
      ];
  }

  updateDeskManager(): void {
    this.deskManagerDetailsService
      .add(
        {
          ...this.userFormGroup.getRawValue(),
          name: this.userFormGroup.getRawValue().firstName,
          id: this.currentUser?.dependentId,
          roleId: this.currentUser?.userRoleId,
          email: this.userFormGroup.getRawValue().emailAddress,
          leaveBalances: this.getLeaveBalanceDto
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getCurrentUser();
          this.updateHeaderProfilePicture();
          this.formSubmittedSuccess.emit();
          this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Profile details'));
          this.leaveData = [];
          this.cdr.detectChanges();
        },
        error: () => {
          this.formSubmittedSuccess.emit();
          this.cdr.detectChanges();
        }
      });
  }

  updateInstructorOrSupervisor(): void {
    this.instructorService
      .add(
        {
          ...this.userFormGroup.getRawValue(),
          name: this.userFormGroup.getRawValue().firstName,
          id: this.currentUser?.dependentId,
          roleId: this.currentUser?.userRoleId,
          email: this.userFormGroup.getRawValue().emailAddress,
          isSupervisor: this.currentUser?.isSupervisor,
          leaveBalances: this.getLeaveBalanceDto
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getCurrentUser();
          this.updateHeaderProfilePicture();
          this.formSubmittedSuccess.emit();
          this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Profile details'));
          this.leaveData = [];
          this.cdr.detectChanges();
        },
        error: () => {
          this.formSubmittedSuccess.emit();
          this.cdr.detectChanges();
        }
      });
  }

  updateStudentProfile(): void {
    if (this.isUserUnderAge(this.userFormGroup.getRawValue().dateOfBirth)) {
      this.formSubmittedSuccess.emit();
      this.toasterService.error(this.constants.errorMessages.userUnderAge);
      return;
    }
    this.profileService
      .update(this.updateProfileParams, this.path.profile.updateCurrentUserProfile)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.formSubmittedSuccess.emit();
          this.updateHeaderProfilePicture();
          if (this.deleteChildIds?.length) {
            this.deleteDependent();
          }
          this.deleteChildIds = [];
          this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Profile details'));
          this.getCurrentUser();
          this.cdr.detectChanges();
        },
        error: () => {
          this.formSubmittedSuccess.emit();
        }
      });
  }

  get updateProfileParams() {
    return {
      ...this.currentUser,
      ...this.userFormGroup.value,
      phoneNumber: this.userFormGroup.controls.phoneNumber.value?.toString()
    };
  }

  updateHeaderProfilePicture(): void {
    if (this.currentUser) {
      this.authService.setCurrentUser$({ ...this.currentUser, ...this.userFormGroup.value } as Account);
    }
  }

  saveProfileImage($event: any): void {
    if ($event.target.files && $event.target.files[0]) {
      this.setShowImageLoader(true);
      this.uploadFileService
        .uploadFile(API_URL.uploadFile.uploadFileToS3, this.getSaveProfilePictureParams($event.target.files[0]))
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res: CBGetResponse<FileUpload>) => {
            if (res.result?.fullPath && res.result.shortPath) {
              this.updateProfilePictureValuesInForm(res.result.fullPath, res.result.shortPath);
            }
            this.updateHeaderProfilePicture();
            this.setShowImageLoader(false);
            this.cdr.detectChanges();
          },
          error: () => {
            this.setShowImageLoader(false);
          }
        });
    }
  }

  getSaveProfilePictureParams(selectedFile: File): FormData {
    const formData = new FormData();
    formData.append('file', selectedFile, selectedFile.name);
    formData.append('uploadType', '1');
    return formData;
  }

  setShowImageLoader(showLoader: boolean): void {
    this.showImageLoader = showLoader;
    this.cdr.detectChanges();
  }

  updateProfilePictureValuesInForm(fullPath: string | null, shortPath: string | null): void {
    this.userFormGroup.patchValue({
      profilePicture: shortPath,
      profilePicturefullurl: fullPath,
      profilePhoto: shortPath,
      profilePic: shortPath
    });
  }

  openConfirmationDialog(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Remove Profile Picture',
        message: 'Are you sure you want to remove profile profile?'
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.removeProfilePicture();
      }
    });
  }

  removeProfilePicture(): void {
    this.setShowImageLoader(true);
    this.uploadFileService
      .deleteFile(`${API_URL.uploadFile.deleteFileFromAws}?${API_URL.uploadFile.fileName}=${this.currentUser?.profilePicture}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.updateProfilePictureValuesInForm(null, null);
          this.updateHeaderProfilePicture();
          this.setShowImageLoader(false);
          this.cdr.detectChanges();
        },
        error: () => {
          this.setShowImageLoader(false);
          this.cdr.detectChanges();
        }
      });
  }

  deleteDependentConfirm(dependentIndex: number, dependentId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Dependent Information`,
        message: `Are you sure you want to delete this Dependent information?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteChildIds.push(dependentId);
        this.getDependentInfoFormArray.removeAt(dependentIndex);
        this.cdr.detectChanges();
      }
    });
  }

  deleteDependent() {
    this.deleteChildIds?.forEach(id => {
      this.dependentService
        .delete(id, API_URL.crud.delete)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.getDependentInfoFormArray.removeAt(id);
            this.cdr.detectChanges();
          },
          error: () => {
            this.cdr.detectChanges();
          }
        });
    });
  }

  initializeUserForm(): void {
    this.userFormGroup = new FormGroup<UserFormGroupType>({
      firstName: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.NAME_PATTERN)]
      }),
      lastName: new FormControl('', {
        nonNullable: true
      }),
      emailAddress: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.EMAIL)]
      }),
      phoneNumber: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, phoneNumberValidator()]
      }),
      address: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      stateId: new FormControl(undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      city: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      zipCode: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, zipCodeValidator()]
      }),
      bio: new FormControl('', {
        nonNullable: true
      }),
      dateOfBirth: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, dateOfBirthValidator()]
      }),
      dependentDetails: new FormArray([] as FormGroup<DependentInfoForm>[]),
      profilePicture: new FormControl('', { nonNullable: true }),
      profilePicturefullurl: new FormControl('', { nonNullable: true }),
      profilePhoto: new FormControl('', { nonNullable: true }),
      profilePic: new FormControl('', { nonNullable: true }),
      instructorInstrument: new FormArray([] as FormGroup<InstructorInstrumentFormGroup>[]),
      instructorAvailability: new FormArray([] as FormGroup<InstructorAvailabilityFormGroup>[]),
      deskManagerAvailabilityAndLocations: new FormArray([] as FormGroup<InstructorAvailabilityFormGroup>[]),
      leaveBalances: new FormArray([] as FormGroup<LeaveBalanceFormGroup>[])
    });
    this.userFormGroup.controls.emailAddress.disable();
    this.cdr.detectChanges();
  }

  addNewInstruments(instrument?: InstructorInstrument): void {
    const formGroup = new FormGroup<Record<string, AbstractControl>>({
      id: new FormControl(0, { nonNullable: true }),
      instrumentId: new FormControl(instrument?.id ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      gradeLevel: new FormControl(instrument?.gradeLevel ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      isIntroductoryClassAvailable: new FormControl(instrument?.isIntroductoryClassAvailable ?? false, {
        nonNullable: true
      })
    });

    if (this.currentUser?.isSupervisor) {
      formGroup.addControl('isHeadOfDepartment', new FormControl(instrument?.isHeadOfDepartment ?? false, { nonNullable: true }));
    }

    this.getInstructorFormArray('instructorInstrument')?.push(formGroup);
  }

  getFormattedStartTime(i: number): string {
    if (this.currentUser?.userRoleId === this.constants.roleIds.DESK_MANAGER) {
      return this.datePipe.transform(this.currentUser?.deskManagerAvailabilityAndLocations[i]?.availableStartTime, 'shortTime') ?? '';
    } else {
      return this.datePipe.transform(this.currentUser?.instructorAvailability[i]?.availableStartTime, 'shortTime') ?? '';
    }
  }

  getFormattedEndTime(i: number): string {
    if (this.currentUser?.userRoleId === this.constants.roleIds.DESK_MANAGER) {
      return this.datePipe.transform(this.currentUser?.deskManagerAvailabilityAndLocations[i]?.availableEndTime, 'shortTime') ?? '';
    } else {
      return this.datePipe.transform(this.currentUser?.instructorAvailability[i]?.availableEndTime, 'shortTime') ?? '';
    }
  }

  addNewLocationAndInstructorAvailability(instructorAvailability?: InstructorAvaibilityInInstructorDetail): void {
    if (this.isInstructor || this.currentUser?.isSupervisor) {
      this.getInstructorFormArray('instructorAvailability')?.push(this.createAvailabilityFormGroup(instructorAvailability));
    }
    if (this.isDeskManager) {
      this.getInstructorFormArray('deskManagerAvailabilityAndLocations')?.push(
        this.createAvailabilityFormGroup(instructorAvailability, true)
      );
    }
  }

  createAvailabilityFormGroup(instructorAvailability: any, isDeskManager: boolean = false): FormGroup {
    const endDate = this.datePipe.transform(instructorAvailability?.availableEndDate, this.constants.dateFormats.yyyy_MM_dd);
    return new FormGroup(
      {
        id: new FormControl(instructorAvailability?.id ?? 0, { nonNullable: true }),
        availableStartTime: new FormControl(instructorAvailability?.availableStartTime ?? '', {
          nonNullable: true,
          validators: [Validators.required, outOfRangeTimeValidator()]
        }),
        availableEndTime: new FormControl(instructorAvailability?.availableEndTime ?? '', {
          nonNullable: true,
          validators: [Validators.required, outOfRangeTimeValidator()]
        }),
        availableStartDate: new FormControl(instructorAvailability?.availableStartDate ?? '', {
          nonNullable: true,
          validators: [Validators.required]
        }),
        availableEndDate: new FormControl(instructorAvailability?.availableEndDate ?? '', { nonNullable: true }),
        availableDays: new FormArray([] as FormControl<number>[]),
        availabilityType: new FormControl(instructorAvailability?.availabilityType ?? undefined, {
          nonNullable: true,
          validators: [Validators.required]
        }),
        neverEnd: new FormControl(endDate === this.getOneYearLaterDate(instructorAvailability?.availableStartDate), {
          nonNullable: true
        }),
        ...(isDeskManager
          ? {
              locationsId: new FormControl(instructorAvailability?.locationsId ?? undefined, {
                nonNullable: true,
                validators: [Validators.required]
              }),
              deskManagerId: new FormControl(this.currentUser?.dependentId, { nonNullable: true })
            }
          : {
              locationId: new FormControl(instructorAvailability?.locationId ?? undefined, {
                nonNullable: true,
                validators: [Validators.required]
              }),
              roomId: new FormControl(instructorAvailability?.roomId ?? undefined, {
                nonNullable: true,
                validators: [Validators.required]
              }),
              roomScheduleSummaryId: new FormControl(instructorAvailability?.roomScheduleSummaryId ?? undefined, {
                nonNullable: true
              })
            })
      },
      { validators: timeRangeValidator('availableStartTime', 'availableEndTime') } as AbstractControlOptions
    );
  }

  getInstructorFormArray(controlName: string): FormArray {
    return this.userFormGroup?.get(controlName) as FormArray;
  }

  confirmationPopup(index: number, isInstrument: boolean): void {
    const name = this.isDeskManager ? 'deskManagerAvailabilityAndLocations' : 'instructorAvailability';
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete ${isInstrument ? 'Instrument' : 'Location & Availability'}`,
        message: `Are you sure you want to delete this ${isInstrument ? 'Instrument' : 'Location & Availability'}?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        isInstrument
          ? this.getInstructorFormArray('instructorInstrument').removeAt(index)
          : this.getInstructorFormArray(name).removeAt(index);
        this.cdr.detectChanges();
      }
    });
  }
}
