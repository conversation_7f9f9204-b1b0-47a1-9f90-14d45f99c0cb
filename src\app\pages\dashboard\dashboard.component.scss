@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.auth-page-wrapper {
  .dashboard-header {
    h1 {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 8px;
      color: $primary-color;
    }

    .dashboard-date {
      font-size: 16px;
      color: $gray-text;
    }
  }

  .o-card {
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    .placeholder-name {
      width: 40px;
      height: 40px;
    }

    &:hover {
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    &.summary-card {
      display: flex;
      cursor: pointer;

      .summary-icon {
        margin-right: 15px;

        img {
          width: 40px;
          height: 40px;
        }
      }

      .summary-content {
        .summary-count {
          font-size: 28px;
          font-weight: 700;
          color: $primary-color;
        }

        .summary-title {
          font-size: 17px;
          font-weight: 600;
          color: $gray-text;
        }
      }
    }

    .timeline-container {
      overflow-x: auto;
      margin-top: 20px;

      &.minHeight {
        max-height: calc(100vh - 200px);
      }

      .timeline-item {
        display: flex;
        min-height: 70px;

        .dot-with-line {
          position: relative;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background-color: $gray-bg-light;
          margin: 0px 10px;
          z-index: 0;
        }

        .dot-with-line::before {
          content: '';
          position: absolute;
          border-left: 1px dashed $btn-options-border-color;
          height: 80px;
          left: 50%;
          z-index: -1;
        }

        .no-line::before {
          display: none;
        }

        .details {
          font-size: 17px;
          line-height: normal;

          .instrument-detail {
            font-weight: 700;
          }

          .additional-info {
            @include flex-content-align-center;
            flex-wrap: wrap;
            color: $gray-text;
            font-size: 14px;

            img {
              height: 16px;
              width: 16px;
              margin-right: 3px;
              filter: $gray-filter;
            }
          }
        }
      }

      .time {
        font-weight: 700;
        color: $gray-text;
        min-width: 70px;
      }
    }

    .card-header {
      @include flex-content-space-between;
      margin-bottom: 20px;

      .card-title {
        font-size: 18px;
        font-weight: 700;
        @include flex-content-align-center;

        ::ng-deep .mat-select-custom {
          .mat-mdc-text-field-wrapper {
            height: 45px;
            width: 330px;
          }

          .mat-mdc-form-field-infix {
            padding-top: 10px !important;
            padding-bottom: 10px !important;
          }

          .mat-mdc-select-placeholder,
          .mat-mdc-select-value-text {
            font-weight: 700 !important;
          }

          .mat-mdc-form-field-subscript-wrapper {
            display: none;
          }
        }

        img {
          width: 22px;
          height: 22px;
          margin-right: 10px;
          filter: $black-filter;
        }

        mat-icon {
          margin-right: 10px;
          color: $primary-color;
        }
      }

      .view-all {
        color: $primary-color;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: darken($primary-color, 10%);
        }
      }
    }

    .card-content {
      .person-item {
        @include flex-content-align-center;

        .person-details {
          flex: 1;

          .person-name {
            font-size: 16px;
            font-weight: 600;
          }

          .person-role {
            font-size: 14px;
            color: $gray-text;
          }
        }

        .enrollment-status {
          padding: 5px 10px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 600;

          &.enrolled {
            background-color: $header-schedule-bg-color;
            color: $primary-color;
          }

          &.notEnrolled {
            background-color: $red-bg-color;
            color: $red-btn;
          }

          &.achPending {
            background-color: $light-yellow-color;
            color: $yellow-color;
          }
        }
      }

      .toggle-btn-wrapper {
        width: fit-content;
      }

      .birthday-avatars {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 15px;
      }

      .birthday-avatar {
        @include flex-content-align-center;
        flex-direction: column;
        width: 80px;

        img {
          width: 40px;
          height: 40px;
          border-radius: 6px;
          margin-bottom: 6px;
        }

        .birthday-name {
          @include ellipse(80px);
          text-align: center;
          font-size: 14px;
          font-weight: 600;
          color: $gray-text;
          margin-top: 4px;
        }

        .birthday-date {
          font-size: 13px;
          font-weight: 600;
          color: $gray-text;
        }
      }

      @media (min-width: 992px) and (max-width: 1199px) {
        .birthday-avatars {
          gap: 8px;
        }

        .birthday-avatar {
          width: 55px;

          img,
          .placeholder-avatar {
            width: 40px;
            height: 40px;
          }

          .birthday-name {
            max-width: 55px;
          }
        }
      }

      .upcoming-title {
        font-size: 16px;
        margin-bottom: 10px;
        font-weight: 600;
      }

      .empty-state {
        @include flex-content-center;
        flex-direction: column;

        img {
          width: 100px;
          height: 100px;
          margin-bottom: 20px;
        }

        h3 {
          font-size: 18px;
          font-weight: 600;
          color: $gray-text;
        }

        p {
          font-size: 14px;
          color: $gray-text;
          text-align: center;
        }
      }
    }

    .instructor-working-hours {
      max-height: calc(100vh - 200px);
      overflow: auto;
    }

    .leave-dashboard {
      @include flex-content-align-center;

      .total-leaves {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 15px;

        .content {
          font-size: 13px;
          font-weight: 400;
          color: $gray-text;
        }
      }

      .used-leaves,
      .available-leaves {
        font-size: 13px;
        font-weight: 600;
        color: $gray-text;
      }

      .dot {
        display: inline-block;
        height: 8px;
        width: 8px;

        &.used-dot {
          background-color: $header-schedule-bg-color;
        }

        &.available-dot {
          background-color: $primary-color;
        }
      }
    }
  }
}

@media (max-width: 767px) {
  .auth-page-wrapper {
    padding: 10px;

    .dashboard-header {
      h1 {
        font-size: 24px;
      }

      .dashboard-date {
        font-size: 14px;
      }
    }

    .o-card {
      padding: 15px;

      &.summary-card {
        padding: 15px;

        .summary-icon {
          width: 50px;
          height: 50px;

          img {
            width: 25px;
            height: 25px;
          }
        }

        .summary-content {
          .summary-count {
            font-size: 24px;
          }

          .summary-title {
            font-size: 14px;
          }
        }
      }

      .card-header {
        .card-title {
          font-size: 16px;

          img {
            width: 20px;
            height: 20px;
          }
        }
      }

      .card-content {
        .event-item {
          flex-direction: column;

          .event-date {
            margin-bottom: 10px;
          }
        }

        .birthday-avatars {
          justify-content: center;

          &.upcoming {
            justify-content: flex-start;
          }
        }
      }
    }
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .auth-page-wrapper {
    .birthday-avatars {
      gap: 8px;
      justify-content: space-around;
    }

    .birthday-avatar {
      width: 55px;

      img,
      .placeholder-avatar {
        width: 40px;
        height: 40px;
      }

      .birthday-name {
        max-width: 55px;
      }
    }
  }
}
