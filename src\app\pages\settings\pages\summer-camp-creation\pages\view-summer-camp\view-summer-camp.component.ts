import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService } from 'src/app/shared/services';
import { CBGetResponse } from 'src/app/shared/models';
import { DashIfEmptyPipe } from 'src/app/shared/pipe';
import { SummerCampDetails } from '../../models';
import { SummerCampScheduleService } from '../../services';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule],
  PIPES: [DashIfEmptyPipe]
};

@Component({
  selector: 'app-view-summer-camp',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  templateUrl: './view-summer-camp.component.html',
  styleUrl: '../../../group-class-creation/pages/view-group-class/view-group-class.component.scss'
})
export class ViewSummerCampComponent extends BaseComponent implements OnChanges {
  @Input() selectedSummerCamp!: SummerCampDetails | null;
  @Input() selectedTabOption!: string | null;

  summerCampDetail!: SummerCampDetails | undefined;

  @Output() closeViewSideNav = new EventEmitter<void>();
  @Output() summerCampUpdated = new EventEmitter<void>();
  @Output() openEditSideNav = new EventEmitter<void>();

  constructor(
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly summerCampScheduleService: SummerCampScheduleService,
    private readonly cdr: ChangeDetectorRef,
    protected readonly schedulerService: SchedulerService
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedSummerCamp']?.currentValue) {
      this.selectedSummerCamp = changes['selectedSummerCamp']?.currentValue;
      this.getSummerCampDetail(this.selectedSummerCamp?.summerCampScheduleSummary.id);
    }
  }

  getSummerCampDetail(id?: number): void {
    this.showPageLoader = true;
    this.summerCampScheduleService
      .get<CBGetResponse<SummerCampDetails>>(
        `${API_URL.summerCampScheduleSummaries.getSummerCampScheduleSummaryForView}?id=${id}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<SummerCampDetails>) => {
          this.summerCampDetail = res.result;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  deleteSummerCampConfirmation(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Summer Camp`,
        message: `Are you sure you want to delete this Summer Camp?`
      }
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result.isConfirmed) {
        this.deleteSummerCamp(this.summerCampDetail!.summerCampScheduleSummary.id);
      }
    });
  }

  deleteSummerCamp(summerCampId: number): void {
    this.summerCampScheduleService
      .delete(summerCampId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.closeViewSideNavFun();
          this.summerCampUpdated.emit();
          this.toasterService.success(
            this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Summer Camp')
          );
          this.cdr.detectChanges();
        }
      });
  }

  navigateToEdit(): void {
    this.openEditSideNav.emit();
  }

  closeViewSideNavFun(): void {
    this.closeViewSideNav.emit();
  }
}
