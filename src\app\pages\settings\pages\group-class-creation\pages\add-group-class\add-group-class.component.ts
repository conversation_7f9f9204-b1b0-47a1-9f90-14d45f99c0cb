import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CommonModule, DatePipe } from '@angular/common';
import { RoomDetails, SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { CBGetResponse, CBResponse } from 'src/app/shared/models';
import { Instrument } from 'src/app/request-information/models';
import { GroupClassFormGroup } from 'src/app/pages/schedule-classes/pages/group-class/models/group-class.model';
import { RoomService } from 'src/app/pages/room-and-location-management/pages/room/services';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { Instructor } from 'src/app/schedule-introductory-lesson/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import {
  ClassTypes,
  InstructorAvaibility,
  SuggestedTimeSlot
} from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { GroupClassesService } from 'src/app/pages/schedule-classes/pages/group-class/services';
import { EnumToKeyValuePipe } from 'src/app/shared/pipe';
import { Duration } from '../../../plan/models/plan-summary.model';
import { RevenueCategory } from '../../../revenue-categories/models';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import moment from 'moment';
import { CommonUtils } from 'src/app/shared/utils';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    SharedModule,
    MatSelectModule,
    CommonModule,
    MatCheckboxModule,
    MatDatepickerModule
  ],
  PIPES: [EnumToKeyValuePipe]
};

@Component({
  selector: 'app-add-group-class',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './add-group-class.component.html',
  styleUrl: './add-group-class.component.scss'
})
export class AddGroupClassComponent extends BaseComponent implements OnInit {
  groupClassFormGroup!: FormGroup<GroupClassFormGroup>;
  locations!: Array<SchoolLocations>;
  instruments!: Array<Instrument>;
  rooms!: Array<RoomDetails>;
  instructors!: Array<Instructor> | undefined;
  revenueCategories!: Array<RevenueCategory>;
  maxDate = new Date();
  durations = Duration;
  classTypes = ClassTypes;
  suggestedTimeSlots!: SuggestedTimeSlot[] | undefined;
  selectedTimeSlot!: string | null;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() isGroupClassAdded = new EventEmitter<void>();

  constructor(
    private readonly datePipe: DatePipe,
    private readonly schedulerService: SchedulerService,
    private readonly toasterService: AppToasterService,
    private readonly roomService: RoomService,
    private readonly commonService: CommonService,
    private readonly groupClassService: GroupClassesService,
    private readonly dialog: MatDialog,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initGroupClassForm();
    this.getLocations();
    this.getInstructors();
    this.getInstruments();
    this.getRevenueCategories();
  }

  initGroupClassForm(): void {
    this.groupClassFormGroup = new FormGroup<GroupClassFormGroup>({
      id: new FormControl(undefined, { nonNullable: true }),
      groupClassName: new FormControl(undefined, { nonNullable: true }),
      instrumentId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      instrumentName: new FormControl('', { nonNullable: true }),
      duration: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      skillType: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      ageGroup: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      lessonType: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      description: new FormControl('', { nonNullable: true }),
      locationId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      roomId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      studentCapacity: new FormControl(this.constants.defaultStudentCapacity, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(1)]
      }),
      isWaitlistAvailable: new FormControl(false, { nonNullable: true, validators: [Validators.required] }),
      scheduleStartDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      enrollLastDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      daysOfSchedule: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      instructorId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      scheduleStartTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      price: new FormControl(undefined, { nonNullable: true, validators: [Validators.required, Validators.min(1)] }),
      categoryId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] })
    });
  }

  getFilterParamsForRooms() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      page: 1,
      locationIdFilter: [this.groupClassFormGroup.controls.locationId.value ?? 0]
    });
  }

  getRooms(): void {
    this.roomService
      .add(this.getFilterParamsForRooms(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RoomDetails>) => {
          this.rooms = res.result.items;
          this.groupClassFormGroup.controls.roomId.reset();
          this.cdr.detectChanges();
        }
      });
  }

  getInstructors(): void {
    const control = this.getInstructorAvailability;
    if (
      control.daysOfSchedule?.length &&
      control.locationId &&
      control.scheduleStartDate &&
      control.scheduleEndDate &&
      control.duration &&
      control.instrumentId &&
      control.skillType
    ) {
      this.schedulerService
        .add(this.getInstructorAvailability, API_URL.scheduleLessonDetails.getAllInstructor)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<Instructor[]>) => {
            this.instructors = response.result;
            this.groupClassFormGroup.controls.instructorId.reset();
            this.cdr.detectChanges();
          }
        });
    }
  }

  getRoomsAndInstructors(): void {
    this.getRooms();
    this.getInstructors();
  }

  setEnrollDate(): void {
    const scheduleStartDate = moment(this.groupClassFormGroup.getRawValue().scheduleStartDate);
    const today = moment().startOf('day');
    const twoDaysBefore =
      scheduleStartDate.diff(today, 'days') > 1
        ? scheduleStartDate.subtract(2, 'days').toDate()
        : scheduleStartDate.toDate();
    this.setFormControlValue('enrollLastDate', twoDaysBefore);
  }

  getSuggestedTimeAndInstructors(clearEndDate: boolean): void {
    if (clearEndDate) {
      this.groupClassFormGroup.controls.scheduleEndDate.reset();
    }
    this.getSuggestedTime();
    this.getInstructors();
  }

  getInstruments(): void {
    this.commonService
    .getInstruments()
    .pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (res: CBResponse<Instrument>) => {
        this.instruments = res.result.items;
        this.cdr.detectChanges();
      } 
    });
  }

  getRevenueCategories(): void {
    this.commonService
      .getRevenueCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RevenueCategory>) => {
          this.revenueCategories = res.result.items;
          const id = this.revenueCategories.find(
            (item) => item.category.categoryName === this.constants.revenueCategories.introductoryGroupClassRevenue
          )?.category.id!;
          this.setFormControlValue('categoryId', id);
          this.cdr.detectChanges();
        }
      });
  }

  setFormControlValue(controlName: string, value: string | number | boolean | Date): void {
    (this.groupClassFormGroup.controls as any)[controlName].setValue(value);
    if (
      controlName === 'daysOfSchedule' ||
      controlName === 'duration' ||
      controlName === 'instrumentId' ||
      controlName === 'skillType'
    ) {
      this.getInstructors();
    }
    if (controlName === 'lessonType' || controlName === 'instrumentName') {
      this.getGroupClassName();
    }
  }

  getGroupClassName(): void {
    if (this.groupClassFormGroup.getRawValue().lessonType && this.groupClassFormGroup.getRawValue().instrumentName) {
      return this.setFormControlValue(
        'groupClassName',
        this.schedulerService.getGroupClassName(
          this.groupClassFormGroup.getRawValue().lessonType! ?? '',
          this.groupClassFormGroup.getRawValue().instrumentName ?? ''
        )
      );
    }
  }

  setStartAndEndTime(selectedTimeSlot: SuggestedTimeSlot): void {
    this.selectedTimeSlot = `${selectedTimeSlot.startTime} - ${selectedTimeSlot.endTime}`;
    this.groupClassFormGroup.patchValue({
      scheduleStartTime: selectedTimeSlot.startTime,
      scheduleEndTime: selectedTimeSlot.endTime
    });
  }

  get getInstructorAvailability(): InstructorAvaibility {
    return {
      classType: this.classTypes.GROUP_CLASS,
      scheduleStartDate: this.datePipe.transform(
        this.groupClassFormGroup.controls.scheduleStartDate.value,
        this.constants.dateFormats.yyyy_MM_dd
      ),
      scheduleEndDate: this.datePipe.transform(
        this.groupClassFormGroup.controls.scheduleEndDate.value,
        this.constants.dateFormats.yyyy_MM_dd
      ),
      daysOfSchedule: this.groupClassFormGroup.controls.daysOfSchedule.value
        ? [Number(this.groupClassFormGroup.controls.daysOfSchedule.value)]
        : [],
      isAllInstances: true,
      instructorId: this.groupClassFormGroup.controls.instructorId.value,
      locationId: this.groupClassFormGroup.controls.locationId.value,
      planId: null,
      duration: this.groupClassFormGroup.controls.duration.value ?? null,
      skillType: this.groupClassFormGroup.controls.skillType.value,
      instrumentId: this.groupClassFormGroup.controls.instrumentId.value
    };
  }

  getSuggestedTime(): void {
    if (
      this.getInstructorAvailability.instructorId &&
      this.getInstructorAvailability.scheduleEndDate &&
      this.getInstructorAvailability.scheduleStartDate
    ) {
      this.schedulerService
        .add(this.getInstructorAvailability, API_URL.scheduleLessonDetails.getInstructorAvaibility)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<SuggestedTimeSlot[]>) => {
            this.suggestedTimeSlots = response.result;
            this.selectedTimeSlot = null;
            this.groupClassFormGroup.controls.scheduleStartTime.reset();
            this.cdr.detectChanges();
          }
        });
    }
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  checkCapacity(): void {
    if (this.groupClassFormGroup.invalid) {
      this.groupClassFormGroup.markAllAsTouched();
      return;
    }
    const selectedRoomId = this.groupClassFormGroup.get('roomId')?.value;
    const selectedRoom = this.rooms.find((room) => room.roomDetail.id === selectedRoomId);
    const formCapacity = this.groupClassFormGroup.getRawValue().studentCapacity;

    if (formCapacity > +selectedRoom?.roomDetail.capacity!) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Alert',
          message: `Selected room capacity is ${selectedRoom?.roomDetail.capacity} which is less than the required capacity.`,
          acceptBtnName: 'Book Anyway',
          rejectBtnName: 'Change'
        }
      });

      dialogRef.afterClosed().subscribe((result) => {
        if (result.isConfirmed) {
          this.onSubmit();
        }
      });
      return;
    }
    this.onSubmit();
  }

  onSubmit(): void {
    this.groupClassFormGroup.markAsUntouched();
    this.showBtnLoader = true;
    this.groupClassService
      .add(
        {
          ...this.groupClassFormGroup.getRawValue(),
          scheduleStartDate: this.datePipe.transform(
            new Date(this.groupClassFormGroup.getRawValue().scheduleStartDate),
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleEndDate: this.datePipe.transform(
            this.groupClassFormGroup.getRawValue().scheduleEndDate,
            this.constants.dateFormats.yyyy_MM_dd
          ),
          enrollLastDate: this.datePipe.transform(
            this.groupClassFormGroup.getRawValue().enrollLastDate,
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleStartTime: this.datePipe.transform(
            this.groupClassFormGroup.getRawValue().scheduleStartTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          scheduleEndTime: this.datePipe.transform(
            this.groupClassFormGroup.getRawValue().scheduleEndTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          daysOfSchedule: [Number(this.groupClassFormGroup.getRawValue().daysOfSchedule)],
          groupClassName:
            this.groupClassFormGroup.getRawValue().groupClassName ??
            this.schedulerService.getGroupClassName(
              this.groupClassFormGroup.getRawValue().lessonType!,
              this.groupClassFormGroup.getRawValue().instrumentName
            )
        },
        API_URL.crud.create
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.isGroupClassAdded.emit();
          this.toasterService.success(
            this.constants.successMessages.addedSuccessfully.replace('{item}', 'Group Class')
          );
          this.closeSideNavFun();
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavFun(): void {
    this.closeSideNav.emit();
    this.groupClassFormGroup.reset();
  }

  asIsOrder() {
    return 1;
  }
}
