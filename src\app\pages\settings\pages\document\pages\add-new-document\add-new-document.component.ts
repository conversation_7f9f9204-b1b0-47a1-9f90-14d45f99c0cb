import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService } from 'src/app/shared/services';
import { DocumentCreationFormGroup, DocumentDetails, DocumentInfo, DocumentTypes } from '../../models';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { DocumentService } from '../../services/document.service';
import { UploadFileService } from 'src/app/shared/services/upload-file.service';
import { CBGetResponse, FileUpload, MatDialogRes } from 'src/app/shared/models';
import { CommonModule } from '@angular/common';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    SharedModule,
    MatProgressBarModule,
    MatSelectModule
  ]
};

@Component({
  selector: 'app-add-new-document',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './add-new-document.component.html',
  styleUrl: './add-new-document.component.scss'
})
export class AddNewDocumentComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedDocDetails!: DocumentInfo | null;

  documentFormGroup!: FormGroup<DocumentCreationFormGroup>;
  documentType = DocumentTypes;
  isShowUploadSpinner!: boolean;
  allowedExtension = this.constants.allowedExtensionPDF;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() documentAddedorEdited = new EventEmitter<void>();

  constructor(
    private readonly documentService: DocumentService,
    private readonly toasterService: AppToasterService,
    private readonly uploadFileService: UploadFileService,
    private readonly dialog: MatDialog,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initDocForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedDocDetails']?.currentValue) {
      this.selectedDocDetails = changes['selectedDocDetails'].currentValue;
      this.documentFormGroup.patchValue({ ...this.selectedDocDetails });
    }
  }

  initDocForm(): void {
    this.documentFormGroup = new FormGroup<DocumentCreationFormGroup>({
      name: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      fileName: new FormControl('', { nonNullable: true }),
      filePath: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      documentType: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      id: new FormControl(undefined, { nonNullable: true })
    });
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file) {
      return;
    }

    if (file.type !== 'application/pdf') {
      this.toasterService.error(this.constants.errorMessages.fileUploadRestrict.replace('{item}', 'PDF'));
      return;
    }

    if (file.size > this.constants.maxFileSizes.MAX_FILE_SIZE_5MB) {
      this.toasterService.error(this.constants.errorMessages.fileSizeExceeds.replace('{item}', 'File').replace('{size}', '5MB'));
      return;
    }

    this.uploadFileToAws(file);
  }

  onFileDropped(file: File): void {
    this.uploadFileToAws(file);
  }

  uploadFileToAws(params: File): void {
    this.isShowUploadSpinner = true;
    this.uploadFileService
      .uploadFile(API_URL.uploadFile.uploadFileToS3, this.getDocumentParams(params))
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<FileUpload>) => {
          if (res.result?.fullPath && res.result.shortPath) {
            this.isShowUploadSpinner = false;
            this.documentFormGroup.controls.filePath.setValue(res.result.shortPath);
            this.toasterService.success(this.constants.successMessages.fileUploadSuccess);
          }
          this.cdr.detectChanges();
        },
        error: () => {
          this.isShowUploadSpinner = false;
          this.cdr.detectChanges();
        }
      });
  }

  getDocumentParams(selectedFile: File): FormData {
    const formData = new FormData();
    formData.append('file', selectedFile, selectedFile.name);
    formData.append('uploadType', '2');
    return formData;
  }

  onInvalidFile(invalidMessage: string): void {
    this.toasterService.error(invalidMessage);
  }

  onRemoveUploadedFileConfirmation(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Remove Uploaded File',
        message: 'Are you sure you want to remove uploaded file?'
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.removeFileFromAws();
      }
    });
  }

  removeFileFromAws(): void {
    this.uploadFileService
      .deleteFile(
        `${API_URL.uploadFile.deleteFileFromAws}?${API_URL.uploadFile.fileName}=${this.documentFormGroup.controls.filePath.value}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.documentFormGroup.controls.filePath.setValue('');
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  onSubmit(): void {
    if (this.documentFormGroup.invalid) {
      this.documentFormGroup.markAllAsTouched();
      return;
    }
    this.documentFormGroup.markAsUntouched();
    this.showBtnLoader = true;
    this.documentService
      .add(this.documentFormGroup.getRawValue(), API_URL.crud.createOrEdit)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showBtnLoader = false;
          this.selectedDocDetails
            ? this.toasterService.success(
                this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Document')
              )
            : this.toasterService.success(
                this.constants.successMessages.savedSuccessfully.replace('{item}', 'Document')
              );
          this.documentAddedorEdited.emit();
          this.documentFormGroup.reset();
          this.closeSideNavFun();
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  closeSideNavFun(): void {
    this.documentFormGroup.reset();
    this.closeSideNav.emit();
  }
}
