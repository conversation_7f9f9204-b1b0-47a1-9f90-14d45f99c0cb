import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { AbstractControlOptions, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBResponse, IdNameModel } from 'src/app/shared/models';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { RoomDetails, RoomInstrumentList, SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { SharedModule } from 'src/app/shared/shared.module';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { RoomService } from 'src/app/pages/room-and-location-management/pages/room/services';
import { AddRoomScheduleFormGroup, RoomParams, RoomSchedule } from '../../models';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { outOfRangeTimeValidator, timeRangeValidator } from 'src/app/shared/validators';
import { RoomScheduleService } from '../../services';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { AuthService } from 'src/app/auth/services';
import { CommonUtils } from 'src/app/shared/utils';
import { SupervisorFilter } from 'src/app/pages/members/pages/supervisors/models';
import { MultiSelectChipsComponent } from 'src/app/shared/components/multi-select-chips/multi-select-chips.component';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatInputModule,
    ReactiveFormsModule,
    MatIconModule,
    FormsModule,
    SharedModule,
    NgxMaterialTimepickerModule
  ],
  COMPONENTS: [MultiSelectChipsComponent]
};

@Component({
  selector: 'app-add-room-schedule',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './add-room-schedule.component.html',
  styleUrl: './add-room-schedule.component.scss'
})
export class AddRoomScheduleComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedRoom!: RoomSchedule | null;

  addRoomScheduleForm!: FormGroup<AddRoomScheduleFormGroup>;
  locations!: Array<SchoolLocations>;
  rooms!: Array<RoomDetails>;
  instructors!: Array<InstructorList>;
  instruments!: RoomInstrumentList[] | undefined;
  maxDate = new Date();
  commonInstruments!: Array<IdNameModel>;
  roomParams: RoomParams = {
    instrumentId: {
      id: 1,
      defaultPlaceholder: 'Select Instruments',
      placeholder: 'Select Instruments',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showMax: 3,
      options: [] as Array<IdNameModel>
    }
  };

  @Output() closeModal = new EventEmitter<void>();
  @Output() refreshScheduleData = new EventEmitter<void>();

  constructor(
    private readonly roomService: RoomService,
    private readonly roomScheduleService: RoomScheduleService,
    private readonly commonService: CommonService,
    private readonly datePipe: DatePipe,
    private readonly toasterService: AppToasterService,
    private readonly instructorService: InstructorService,
    private readonly authService: AuthService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initAddRoomScheduleForm();
    this.getCurrentUser();
    this.getLocations();
    if (this.selectedRoom) {
      this.setRoomDetails();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedRoom']?.currentValue) {
      this.selectedRoom = changes['selectedRoom']?.currentValue;
      this.initAddRoomScheduleForm();
    }
  }

  initAddRoomScheduleForm(): void {
    this.addRoomScheduleForm = new FormGroup<AddRoomScheduleFormGroup>(
      {
        id: new FormControl(undefined, { nonNullable: true }),
        isAllInstances: new FormControl(false, { nonNullable: true }),
        locationId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
        roomId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
        instructorId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
        scheduleStartDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
        scheduleEndDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
        instumentIds: new FormControl([], { nonNullable: true, validators: [Validators.required] }),
        scheduleDays: new FormControl([], { nonNullable: true, validators: [Validators.required] }),
        start: new FormControl('', { nonNullable: true, validators: [Validators.required, outOfRangeTimeValidator()] }),
        end: new FormControl('', { nonNullable: true, validators: [Validators.required, outOfRangeTimeValidator()] })
      },
      { validators: timeRangeValidator('start', 'end') } as AbstractControlOptions
    );
  }

  getCurrentUser(): void {
    if (this.selectedRoom) {
      this.showPageLoader = true;
    }
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParamsForRooms() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      page: 1,
      locationIdFilter: [this.addRoomScheduleForm.controls.locationId.value]
    });
  }

  getRooms(): void {
    if (!this.addRoomScheduleForm.controls.locationId.value) {
      return;
    }
    this.roomService
      .add(this.getFilterParamsForRooms(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RoomDetails>) => {
          this.rooms = res.result.items;
          this.getInstructors();
          this.cdr.detectChanges();
        }
      });
  }

  getFormattedTime(controlName: string): string {
    return this.datePipe.transform(controlName === 'start' ? this.selectedRoom?.start : this.selectedRoom?.end, 'shortTime') ?? '';
  }

  setRoomDetails(): void {
    this.addRoomScheduleForm.patchValue({
      ...this.selectedRoom,
      roomId: this.selectedRoom?.resource,
      scheduleStartDate: this.selectedRoom?.scheduleStartDate,
      scheduleEndDate: this.selectedRoom?.scheduleEndDate,
      instructorId: this.selectedRoom?.instructorDetails.id,
      instumentIds: this.selectedRoom?.scheduledInstruments.map(instrument => instrument.id),
      start: this.getFormattedTime('start') ?? '',
      end: this.getFormattedTime('end') ?? ''
    });
    this.getRooms();
    this.onRecurringEditOptionChange(false);
    this.getInstrumentNames(this.selectedRoom?.roomInstruments);
    this.setInstruments();
    this.setTime('start');
    this.setTime('end');
  }

  setInstruments(): void {
    this.roomParams.instrumentId.value =
      this.selectedRoom?.scheduledInstruments.map(instrument => ({
        id: instrument.id,
        name: instrument.instrumentName ?? ''
      })) ?? [];
  }

  getInstrumentNames(instrumentList?: RoomInstrumentList[]): void {
    this.instruments = instrumentList;
    if (!this.selectedRoom) {
      this.addRoomScheduleForm.controls.instumentIds.reset();
      this.roomParams.instrumentId.value = [];
    }
    this.getCommonInstruments();
    this.cdr.detectChanges();
  }

  getCommonInstruments(): void {
    this.commonInstruments = [];
    const selectedInstructor = this.instructors?.find(instructor => {
      return instructor.instructorDetail.id === this.addRoomScheduleForm.controls.instructorId.value;
    });
    if (this.instruments?.length && this.addRoomScheduleForm.controls.instructorId.value) {
      this.commonInstruments = this.instruments
        ?.filter(item1 => selectedInstructor?.instructorDetail.instruments.some(item2 => item1.instrumentId === item2.id))
        .map(({ instrumentName, instrumentId }) => {
          return { id: instrumentId, name: instrumentName ?? '' };
        });
    } else if (this.instruments?.length) {
      this.commonInstruments = this.instruments.map(({ instrumentName, instrumentId }) => {
        return { id: instrumentId, name: instrumentName ?? '' };
      });
    } else if (this.addRoomScheduleForm.controls.instructorId.value && selectedInstructor) {
      this.commonInstruments = selectedInstructor.instructorDetail.instruments.map(({ id, name }) => {
        return { id, name: name };
      });
    } else {
      this.commonInstruments = [];
    }
    this.roomParams.instrumentId.options = this.commonInstruments;
    this.roomParams.instrumentId.totalCount = this.commonInstruments.length;
    this.cdr.detectChanges();
  }

  getFilterParamsForInstructors() {
    const instrumentIds = this.instruments?.map(({ instrumentId }) => instrumentId);
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      SupervisorIdFilter: this.currentUser?.isSupervisor ? this.currentUser?.dependentId : null,
      IsSupervisorFilter: SupervisorFilter.ALL,
      LocationIdFilter: [this.addRoomScheduleForm.getRawValue().locationId],
      Page: 1,
      InstrumentIdFilter: instrumentIds
    });
  }

  getInstructors(): void {
    if (this.addRoomScheduleForm.controls.locationId.value) {
      this.instructorService
        .add(this.getFilterParamsForInstructors(), `${API_URL.crud.getAll}`)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res: CBResponse<InstructorList>) => {
            this.showPageLoader = false;
            this.instructors = res.result.items;
            this.getCommonInstruments();
            this.cdr.detectChanges();
          },
          error: () => {
            this.showPageLoader = false;
            this.cdr.detectChanges();
          }
        });
    }
  }

  resetInstructors(): void {
    this.addRoomScheduleForm.controls.instructorId.reset();
    this.addRoomScheduleForm.controls.roomId.reset();
    this.getInstructors();
  }

  resetInstruments(): void {
    this.addRoomScheduleForm.controls.instumentIds.reset();
    this.roomParams.instrumentId.value = [];
  }

  setFormControlValue(controlName: string, value: number | string | boolean | number[]): void {
    (this.addRoomScheduleForm.controls as any)[controlName].setValue(value);
  }

  onRecurringEditOptionChange(editSeries: boolean): void {
    this.setRequiredBasedOnCondition('scheduleDays', editSeries);
    this.setFormControlValue('scheduleDays', []);
    this.setFormControlValue('scheduleStartDate', this.selectedRoom?.scheduleDate ?? '');
    this.setFormControlValue('scheduleEndDate', this.selectedRoom?.scheduleDate ?? '');

    if (this.selectedRoom?.scheduleDays) {
      this.selectedRoom.scheduleDays.forEach(day => {
        this.setFormControlValue('scheduleDays', [...this.addRoomScheduleForm.controls.scheduleDays.value, day]);
      });
    }
    if (editSeries) {
      this.setFormControlValue('scheduleStartDate', this.selectedRoom?.scheduleStartDate ?? '');
      this.setFormControlValue('scheduleEndDate', this.selectedRoom?.scheduleEndDate ?? '');
    }
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean): void {
    const control = this.addRoomScheduleForm.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  onChangeScheduleDate(): void {
    this.setFormControlValue('scheduleEndDate', this.addRoomScheduleForm.controls.scheduleStartDate.value);
  }

  setTime(control: string): void {
    const currentDate = this.datePipe.transform(
      this.selectedRoom ? this.addRoomScheduleForm.controls.scheduleStartDate.value : this.maxDate,
      this.constants.dateFormats.yyyy_MM_dd
    );
    const timeValue = this.datePipe.transform(
      new Date(`${currentDate} ${this.addRoomScheduleForm.get(control)?.value}`),
      this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
    );

    this.addRoomScheduleForm.get(control)?.setValue(timeValue ?? '');
  }

  setInstrumentIds(): void {
    this.addRoomScheduleForm.controls.instumentIds.setValue(this.roomParams.instrumentId.value.map(item => item.id));
  }

  isDaySelected(day: number): boolean {
    return this.addRoomScheduleForm.controls.scheduleDays.value.includes(day);
  }

  setDaysOfWeek(day: number): void {
    const scheduleDays = this.addRoomScheduleForm.controls.scheduleDays.value;
    if (scheduleDays.includes(day)) {
      scheduleDays.splice(scheduleDays.indexOf(day), 1);
    } else {
      scheduleDays.push(day);
    }
    this.addRoomScheduleForm.controls.scheduleDays.setValue(scheduleDays);
    this.cdr.detectChanges();
  }

  onAddRoomSchedule(): void {
    if (this.addRoomScheduleForm.invalid) {
      this.addRoomScheduleForm.markAllAsTouched();
      return;
    }
    this.addRoomScheduleForm.markAsUntouched();
    this.showBtnLoader = true;
    this.roomScheduleService
      .add(
        {
          ...this.addRoomScheduleForm.getRawValue(),
          scheduleStartDate: this.datePipe.transform(
            new Date(this.addRoomScheduleForm.getRawValue().scheduleStartDate),
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleEndDate: this.datePipe.transform(
            new Date(this.addRoomScheduleForm.getRawValue().scheduleEndDate),
            this.constants.dateFormats.yyyy_MM_dd
          )
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.onCloseModal();
          this.refreshScheduleData.emit();
          this.selectedRoom
            ? this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Room schedule'))
            : this.toasterService.success(this.constants.successMessages.addedSuccessfully.replace('{item}', 'Room schedule'));
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onCloseModal(): void {
    this.addRoomScheduleForm.reset();
    this.closeModal.emit();
  }
}
