import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { NgxEchartsModule } from 'ngx-echarts';
import { ImageCropperModule } from 'ngx-image-cropper';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { ProgressBarModule } from 'primeng/progressbar';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ChartViewerComponent } from './chart-viewer/chart-viewer.component';
import { CropImageComponent } from './crop-image/crop-image.component';
import { ErrorHandlerComponent } from './error-handler/error-handler.component';
import { SplashScreenComponent } from './splash-screen/splash-screen.component';
import { UploadProgressComponent } from './upload-progress/upload-progress.component';
import { ContentLoaderComponent } from './content-loader/content-loader.component';
import { ConfirmationDialogComponent } from './confirmation-dialog/confirmation-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { EmptyStateComponent } from './empty-state/empty-state.component';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';

const COMPONENTS = [
  ErrorHandlerComponent,
  SplashScreenComponent,
  CropImageComponent,
  UploadProgressComponent,
  ChartViewerComponent,
  ContentLoaderComponent,
  ConfirmationDialogComponent,
  EmptyStateComponent
];

@NgModule({
  declarations: [...COMPONENTS],
  imports: [
    CommonModule,
    ImageCropperModule,
    DialogModule,
    ButtonModule,
    ProgressSpinnerModule,
    ProgressBarModule,
    NgxEchartsModule.forRoot({
      echarts: () => import('echarts')
    }),
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule
  ],
  exports: [...COMPONENTS, NgxEchartsModule]
})
export class SharedComponentsModule {}
