import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { CustomerOrdersRes, ProductStatus, PurchasedItems } from 'src/app/pages/shop/models';
import { StoreProductService } from 'src/app/pages/shop/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, MatDialogRes } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { DependentInformations } from '../../models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ContinueToCheckoutComponent } from '../continue-to-checkout/continue-to-checkout.component';
import { AppToasterService } from 'src/app/shared/services';
import { PaymentService } from 'src/app/pages/schedule-classes/services';

const DEPENDENCIES = {
  MODULES: [MatButtonModule, SharedModule, CommonModule, MatIconModule, MatTooltipModule, MatSidenavModule],
  COMPONENTS: [ContinueToCheckoutComponent]
};

@Component({
  selector: 'app-view-shopping-cart',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './view-shopping-cart.component.html',
  styleUrl: './view-shopping-cart.component.scss'
})
export class ViewShoppingCartComponent extends BaseComponent implements OnInit {
  @Input() selectedStudentDetails!: DependentInformations | undefined;

  totalAmount!: number;
  totalCount!: number;
  productStatus = ProductStatus;
  isContinueToCheckoutSideNavOpen = false;
  shoppingCart: Array<CustomerOrdersRes> = [];
  cartId = 0;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() addMoreProductEmit = new EventEmitter<void>();
  @Output() refreshProductList = new EventEmitter<void>();
  @Output() closeAllSideNav = new EventEmitter<void>();

  constructor(
    private readonly storeProductService: StoreProductService,
    private readonly paymentService: PaymentService,
    private readonly toasterService: AppToasterService,
    private readonly dialog: MatDialog,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getShoppingCart();
  }

  getShoppingCart(): void {
    this.showPageLoader = true;
    this.storeProductService
      .getListWithFilters<CBGetResponse<PurchasedItems>>(
        {
          StudentId: this.selectedStudentDetails?.id,
          UserId: this.selectedStudentDetails?.accountManagerId,
          LocationId: this.selectedStudentDetails?.locationId
        },
        API_URL.storeProduct.getProductCartView
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<PurchasedItems>) => {
          this.shoppingCart = res.result.cartItems || [];
          this.cartId = res.result.cartId;
          this.totalCount = res.result.cartItems?.length || 0;
          this.calculateTotal();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  calculateTotal(): void {
    this.totalAmount = this.shoppingCart.reduce((sum, item) => {
      return sum + (item.totalPrice || item.productPrice * item.quantity);
    }, 0);
  }

  updateQuantity(item: CustomerOrdersRes, quantity: number): void {
    if (item.status === ProductStatus.OUT_OF_STOCK) {
      return;
    }

    if (quantity <= 0) {
      this.removeSingleItemFromCart(item);
      return;
    }

    const payload = [
      {
        storeProductId: item.id,
        quantity: quantity,
        userId: this.selectedStudentDetails?.accountManagerId,
        studentId: this.selectedStudentDetails?.id,
        locationId: this.selectedStudentDetails?.locationId,
        status: ProductStatus.IN_CART
      }
    ];

    this.storeProductService
      .add(payload, API_URL.storeProduct.checkoutCartItems)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          const itemIndex = this.shoppingCart.findIndex(p => p.id === item.id);
          if (itemIndex !== -1 && itemIndex !== undefined) {
            this.shoppingCart[itemIndex].quantity = quantity;
            this.shoppingCart[itemIndex].totalPrice = this.shoppingCart[itemIndex].productPrice * quantity;
          }
          this.calculateTotal();
          this.refreshProductList.emit();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  openConfirmationPopup(item: CustomerOrdersRes): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Remove from Cart',
        message: `Are you sure you want to remove this item from the cart?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.removeSingleItemFromCart(item);
      }
    });
  }

  removeMultipleItemsFromCart(items: Array<CustomerOrdersRes>): void {
    const payload = items.map(item => ({
      storeProductId: item.id,
      quantity: 0,
      userId: this.selectedStudentDetails?.accountManagerId,
      studentId: this.selectedStudentDetails?.id,
      locationId: this.selectedStudentDetails?.locationId,
      status: ProductStatus.AVAILABLE
    }));
    this.removeFromCartAPI(payload);
  }

  removeSingleItemFromCart(item: CustomerOrdersRes): void {
    const payload = [
      {
        storeProductId: item.id,
        quantity: 0,
        userId: this.selectedStudentDetails?.accountManagerId,
        studentId: this.selectedStudentDetails?.id,
        locationId: this.selectedStudentDetails?.locationId,
        status: ProductStatus.AVAILABLE
      }
    ];
    this.removeFromCartAPI(payload);
  }

  removeFromCartAPI(payload: any): void {
    const removedIds = payload.map((p: any) => p.storeProductId);
    this.storeProductService
      .add(payload, API_URL.storeProduct.checkoutCartItems)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.shoppingCart = this.shoppingCart.filter(item => !removedIds.includes(item.id));
          this.totalCount = this.shoppingCart?.length;
          this.calculateTotal();
          this.refreshProductList.emit();
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  onCheckout(): void {
    if (!this.shoppingCart?.length) {
      this.toasterService.error(this.constants.errorMessages.addItemsToCart);
      return;
    }
    const outOfStockItems = this.shoppingCart.filter(item => item.status === ProductStatus.OUT_OF_STOCK);

    if (outOfStockItems.length) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Out of Stock',
          message: 'Some items in your cart are out of stock. Would you like to remove them and proceed?',
          acceptBtnName: 'Remove Items',
          rejectBtnName: 'Cancel'
        }
      });

      dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
        if (result.isConfirmed) {
          this.removeMultipleItemsFromCart(outOfStockItems);
          const removedIds = outOfStockItems.map((p: any) => p.id);
          this.shoppingCart = this.shoppingCart.filter(item => !removedIds.includes(item.id));
          if (this.shoppingCart.length) {
            this.isContinueToCheckoutSideNavOpen = true;
          }
        }
      });
      return;
    }

    this.isContinueToCheckoutSideNavOpen = true;
    this.paymentService.setUserPayment(null);
  }

  onClose(): void {
    this.closeSideNav.emit();
  }

  closeAll(): void {
    this.isContinueToCheckoutSideNavOpen = false;
    this.closeAllSideNav.emit();
  }

  addMoreProduct(): void {
    this.addMoreProductEmit.emit();
  }
}
