import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService } from 'src/app/shared/services';
import { CommonModule } from '@angular/common';
import {
  RoomDetails,
  RoomDetailsFormGroup,
  RoomInfo,
  RoomInstrumentList,
  RoomInstrumentListFormGroup,
  SchoolLocations
} from 'src/app/pages/room-and-location-management/models';
import { MatButtonModule } from '@angular/material/button';
import { CBResponse, MatDialogRes } from 'src/app/shared/models';
import { AuthService } from 'src/app/auth/services';
import { MatSelectModule } from '@angular/material/select';
import { InstrumentsService } from 'src/app/request-information/services';
import { Instrument } from 'src/app/request-information/models';
import { RoomInstrumentService, RoomService } from '../../services';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatRadioModule } from '@angular/material/radio';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatInputModule,
    MatSelectModule,
    MatFormFieldModule,
    SharedModule,
    MatRadioModule
  ]
};
@Component({
  selector: 'app-add-new-room',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './add-new-room.component.html',
  styleUrl: './add-new-room.component.scss'
})
export class AddNewRoomComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedRoomDetails!: RoomInfo | null;
  @Input() selectedRoomViewDetails!: RoomDetails | null;

  roomFormGroup!: FormGroup<RoomDetailsFormGroup>;
  schoolLocations!: Array<SchoolLocations>;
  instrumentTypes!: Array<Instrument>;
  roomId!: number;
  selectedInstrumedId!: number;
  selectedInstrumentName!: string;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() roomAddedOrEdited = new EventEmitter<boolean>();
  @Output() openViewSideNav = new EventEmitter<RoomDetails | null>();

  constructor(
    private readonly roomService: RoomService,
    private readonly authService: AuthService,
    private readonly instrumentsService: InstrumentsService,
    private readonly roomInstrumentService: RoomInstrumentService,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initRoomForm();
    this.getAllLocations();
    if (!this.selectedRoomDetails) {
      this.addNewInstruments();
    }
    this.getInstruments();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedRoomDetails']?.currentValue) {
      this.selectedRoomDetails = changes['selectedRoomDetails'].currentValue;
      this.roomId = changes['selectedRoomDetails'].currentValue.id;
      this.initRoomForm();
    }
  }

  initRoomForm(): void {
    this.roomFormGroup = new FormGroup<RoomDetailsFormGroup>({
      id: new FormControl(undefined, { nonNullable: true }),
      roomName: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      capacity: new FormControl('', { nonNullable: true, validators: [Validators.required, Validators.min(1)] }),
      locationId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      isVirtualCapable: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      roomInstrumentList: new FormArray([] as FormGroup<RoomInstrumentListFormGroup>[])
    });
    this.setRoomFormValues();
  }

  setRoomFormValues(): void {
    if (this.selectedRoomDetails) {
      this.roomFormGroup.patchValue({ ...this.selectedRoomDetails });
      this.setInstruments(this.selectedRoomDetails?.roomInstrumentList);
      this.cdr.detectChanges();
    }
  }

  get getRoomInstrumentFormArray(): FormArray {
    return this.roomFormGroup.get('roomInstrumentList') as FormArray;
  }

  addNewInstruments(instrument?: RoomInstrumentList): void {
    this.getRoomInstrumentFormArray.push(
      new FormGroup({
        id: new FormControl(instrument?.id ?? 0, { nonNullable: true }),
        instrumentId: new FormControl(instrument?.instrumentId ?? this.selectedInstrumedId, {
          nonNullable: true,
          validators: [Validators.required]
        }),
        quantity: new FormControl(instrument?.quantity, {
          nonNullable: true,
          validators: [Validators.required, Validators.min(1)]
        }),
        roomId: new FormControl(instrument?.roomId ?? this.roomId, { nonNullable: true })
      })
    );
  }

  getAllLocations(): void {
    this.authService
      .getAllLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.schoolLocations = res.result.items;
          this.setRoomFormValues();
        }
      });
  }

  getInstruments(): void {
    this.showPageLoader = true;
    this.instrumentsService
      .getList<CBResponse<Instrument>>(API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instrumentTypes = res.result.items;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  deleteInstrumentConfirmation(index: number, id: number) {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Instrument`,
        message: `Are you sure you want to delete this Instrument?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.getRoomInstrumentFormArray.removeAt(index);
        this.cdr.detectChanges();

        if (this.selectedRoomDetails) {
          this.deleteInstrument(id);
        }
      }
    });
  }

  deleteInstrument(instrumentId: number) {
    this.roomInstrumentService
      .delete(instrumentId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(
            this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Instrument')
          );
          this.cdr.detectChanges();
        }
      });
  }

  checkIfDuplicateInstrumentsExists(): boolean {
    const instrumentIds = this.getRoomInstrumentFormArray.controls.map((control) => control.get('instrumentId')?.value);

    const uniqueInstrumentIds = new Set(instrumentIds);
    return uniqueInstrumentIds.size !== instrumentIds.length;
  }

  onSubmit(): void {
    this.roomAddedOrEdited.emit(false);

    if (this.checkIfDuplicateInstrumentsExists()) {
      this.toasterService.error(this.constants.errorMessages.duplicateInstruments);
      return;
    }
    if (this.roomFormGroup.invalid) {
      this.roomFormGroup.markAllAsTouched();
      return;
    }
    this.roomFormGroup.markAsUntouched();
    this.showBtnLoader = true;
    this.roomService
      .add(
        { ...this.roomFormGroup.getRawValue(), capacity: this.roomFormGroup.getRawValue().capacity.toString() },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.selectedRoomDetails
          ? this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Room'))
          : this.toasterService.success(this.constants.successMessages.savedSuccessfully.replace('{item}', 'Room'));
          this.roomAddedOrEdited.emit(true);
          this.closeSideNavFun();
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  setInstruments(instruments: RoomInstrumentList[] | undefined): void {
    this.getRoomInstrumentFormArray.clear();
    instruments?.forEach((instrument) => {
      this.addNewInstruments(instrument);
    });
  }

  closeSideNavFun(): void {
    this.getRoomInstrumentFormArray.clear();
    this.addNewInstruments();
    this.roomFormGroup.reset();
    this.closeSideNav.emit();
    if (this.selectedRoomViewDetails) {
      this.openViewSideNav.emit(this.selectedRoomViewDetails);
    }
  }
}
