<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isBookAssignedPlanSideNavOpen || isEnsembleClassesSideNavOpen || isAssignProductSideNavOpen || isShoppingCartSideNavOpen || isViewProductSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-850"
    [disableClose]="true">
    @if (isBookAssignedPlanSideNavOpen) {
      <app-book-assigned-plan
        [selectedStudentDetails]="selectedStudentDetails"
        [selectedStudentPlan]="selectedStudentPlan"
        (closeSideNav)="updateAssignedPlan()"
        (closeAllSideNav)="closeAll()"></app-book-assigned-plan>
    }
    @if (isEnsembleClassesSideNavOpen) {
      <app-available-ensemble-list
        [selectedStudentDetails]="selectedStudentDetails"
        [selectedStudentPlan]="selectedStudentPlan"
        (closeSideNav)="updateAssignedPlan()"
        (closeAllSideNav)="closeAll()"
      ></app-available-ensemble-list>
    }
    @if (isAssignProductSideNavOpen) {
      <app-assign-product
        [selectedStudentDetails]="selectedStudentDetails"
        (closeSideNav)="isAssignProductSideNavOpen = false"
        (closeAllSideNav)="closeAll()"
        (cartUpdated)="getShoppingCart()"
      ></app-assign-product>
    }
    @if (isShoppingCartSideNavOpen) {
      <app-view-shopping-cart
        [selectedStudentDetails]="selectedStudentDetails"
        (closeSideNav)="isShoppingCartSideNavOpen = false"
        (closeAllSideNav)="closeAll()"
        (refreshProductList)="getShoppingCart()"
        (addMoreProductEmit)="isAssignProductSideNavOpen = true; isShoppingCartSideNavOpen = false"
        ></app-view-shopping-cart>
    }
    @if (isViewProductSideNavOpen) {
      <app-cart-details [selectedCartItems]="selectedProduct" (closeSideNav)="toggleCartItem(false, null)"></app-cart-details>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="back-btn-wrapper" (click)="closeAssignPlanAndProduct()">
      <img [src]="constants.staticImages.icons.arrowLeft" class="pointer" alt="" />
      <div class="ps-2">
        <div class="title">Assign Plan & Product</div>
        <div class="name">
          <img [src]="constants.staticImages.icons.profileIcon" class="pe-1" alt="" />
          {{ selectedStudentDetails?.firstName | titlecase }}
          {{ selectedStudentDetails?.lastName | titlecase }}
        </div>
      </div>
    </div>
    <div class="action-btn-wrapper">
      <div class="cart-wrapper" (click)="isShoppingCartSideNavOpen = true">
        <img [src]="constants.staticImages.icons.shoppingBag" class="me-3" height="33" width="33" alt="" />
        <div class="cart-count" *ngIf="totalItemsInCart">{{ totalItemsInCart }}</div>
      </div>
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeAssignPlanAndProduct()">
        Close
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <div class="select-assign-items mb-3">
      <div class="title">Select Assign Items</div>
      <div class="btn-typed-options-wrapper">
        @for (assignItemType of assignedItemTypes | enumToKeyValue; track $index) {
          <div
             [ngClass]="{ 'btn-typed-option btn-typed-option-wrap': true, 'active': activeEnumValue === assignItemType.value }"
             class="select-btn"
             (click)="activeEnumValue = assignItemType.value" >
             {{ assignItemType.key | titlecase }}
          </div>
        }
      </div>
    </div>
    <ng-container [ngTemplateOutlet]="activeEnumValue === assignedItemTypes.PLANS ? planTab : productTab"></ng-container>
  </div>
</div>

<ng-template #planTab>
  <div class="accordion-wrapper">
    <div class="accordion-header" (click)="toggleAccordion(accordionType.ASSIGNED_PLAN)">
      <div class="accordion-title">Assigned Plan</div>
      <mat-icon class="accordion-icon">{{
        isAssignedPlanAccordionOpen ? "keyboard_arrow_up" : "keyboard_arrow_down"
      }}</mat-icon>
    </div>
    <div class="accordion-content" *ngIf="isAssignedPlanAccordionOpen">
      <ng-container [ngTemplateOutlet]="showAssignedPlanLoader ? showLoader : assignedPlanAccordion"></ng-container>
    </div>
  </div>

  <div class="accordion-wrapper mt-4">
    <div class="accordion-header" (click)="toggleAccordion(accordionType.ENSEMBLE_PLAN)">
      <div class="accordion-title">Ensemble Plan</div>
      <mat-icon class="accordion-icon">{{
        isEnsemblePlanAccordionOpen ? "keyboard_arrow_up" : "keyboard_arrow_down"
      }}</mat-icon>
    </div>
    <div class="accordion-content" *ngIf="isEnsemblePlanAccordionOpen">
      <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : otherPlansAccordion" [ngTemplateOutletContext]="{ $implicit: ensemblePlans }"></ng-container>
    </div>
  </div>

  <div class="accordion-wrapper mt-4">
    <div class="accordion-header">
      <div class="accordion-title">Other Plan</div>
      <div class="accordion-filters">
        <mat-form-field class="search-bar-wrapper">
          <mat-select [(ngModel)]="filters.planTypeFilter" (selectionChange)="getPlanDetail()">
            <mat-option [value]="all.ALL">All Plan Type</mat-option>
            <mat-option *ngFor="let planType of planTypes | enumToKeyValue" [value]="planType.value">
              {{ planType.key | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="search-bar-wrapper">
          <mat-select [(ngModel)]="filters.DurationFilter" (selectionChange)="getPlanDetail()">
            <mat-option [value]="all.ALL">All Duration</mat-option>
            <mat-option *ngFor="let duration of durations | enumToKeyValue" [value]="duration.value">
              {{ duration.value }} Minutes
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="search-bar-wrapper">
          <mat-select [(ngModel)]="filters.VisitsPerWeekFilter" (selectionChange)="getPlanDetail()">
            <mat-option [value]="all.ALL">All Visits per week</mat-option>
            <mat-option *ngFor="let visit of visits | enumToKeyValue" [value]="visit.value">
              {{ visit.value }} Visit
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <mat-icon class="accordion-icon" (click)="toggleAccordion(accordionType.OTHER_PLAN)">{{
        isOtherPlanAccordionOpen ? "keyboard_arrow_up" : "keyboard_arrow_down"
      }}</mat-icon>
    </div>
    <div class="accordion-content" *ngIf="isOtherPlanAccordionOpen">
      <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : otherPlansAccordion" [ngTemplateOutletContext]="{ $implicit: otherPlans }"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #productTab>
  <div class="accordion-wrapper">
    <div class="accordion-header">
      <div class="accordion-title">Assigned Products</div>
      <div class="accordion-header-btn">
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn me-2"
        type="button"
        (click)="isAssignProductSideNavOpen = true"
        [appLoader]="showBtnLoader">
        Assign Product
      </button>
      <mat-icon class="accordion-icon" (click)="toggleAccordion(accordionType.ASSIGNED_PRODUCT)">{{
        isAssignedProductAccordionOpen ? "keyboard_arrow_up" : "keyboard_arrow_down"
      }}</mat-icon>
      </div>
    </div>
    <div class="accordion-content" *ngIf="isAssignedProductAccordionOpen">
      <ng-container [ngTemplateOutlet]="showAssignedProductLoader ? showLoader : assignedProductAccordion"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #assignedPlanAccordion>
  @for (plan of studentPlans; track $index) {
    <div class="accordion-item" *ngIf="studentPlans?.length">
      <div>
        <div class="plan-name">
          Weekly Music Lessons ({{ planSummaryService.getPlanType(plan.planType) }}-{{
            planSummaryService.getPlanSummary(plan.planDetails)
          }})
        </div>
        <div class="plan-content">
          @if (!plan.isEnsembleAvailable) {
            <div class="fw-bold">{{ plan.instrumentName }}</div>
          }
          @else {
            <div class="fw-bold">Ensemble Plan</div>
          }
          <div class="dot"></div>
          <div class="text-gray">{{ planSummaryService.getTotalVisits(plan.planDetails) }} visits per week</div>
          <div class="dot"></div>
          <div class="text-gray">{{ plan.plan === plansEnum.RECURRING ? "Recurring" : "Custom" }} plan</div>
        </div>
      </div>
      <div class="plan-pricing assigned-plan">
        <div>
          <div class="fw-bold">${{ plan.planPrice }} / <span class="text-gray">Month</span></div>
          @switch (plan.assignedPlanStatus) {
            @case (assignedPlanStatuses.ASSIGNED_PLAN) {
              <button
                mat-raised-button
                color="primary"
                class="mat-primary-btn"
                type="button"
                (click)="onSchedule(plan)"
                [appLoader]="showBtnLoader">
                {{ plan.isEnsembleAvailable ? 'Enroll' : 'Schedule' }}
              </button>
            }
            @case (assignedPlanStatuses.SCHEDULE_CREATED) {
              <div class="text-gray">Payment Pending</div>
            }
            @case (assignedPlanStatuses.PAYMENT_DONE) {
              <div class="text-gray">Assigned</div>
            }
          }
        </div>
        <div class="plan-cancel-btn" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]" (click)="openCancelPlanRequest(plan.studentplan.id ?? 0)" matTooltip="Cancel Plan">
          <img [src]="constants.staticImages.icons.crossCircle" alt="" class="cross-circle">
        </div>
      </div>
    </div>
  }
  <div *ngIf="!studentPlans?.length">
    <div class="accordion-item fw-bold">No Assigned Plan</div>
  </div>
</ng-template>

<ng-template #otherPlansAccordion let-plans>
  @if (plans === ensemblePlans && isEnsembleClassAvailable) {
    <div class="accordion-item fw-bold">Student already has an ensemble plan.</div>
  }

  @for (plan of plans; track $index) {
    @if (!(plan.planSummary.isEnsembleAvailable && isEnsembleClassAvailable)) {
      <div class="accordion-item">
        <div>
          <div class="filters-and-content">
            <div class="plan-name">
              Weekly Music Lessons ({{ planSummaryService.getPlanType(plan.planSummary.planType) }}-{{
                planSummaryService.getPlanSummary(plan.planSummary.plandetails.items)
              }})
            </div>
            @if (!plan.planSummary.isEnsembleAvailable) {
              <div class="dot"></div>
              <div class="accordion-filters other-plan">
                <mat-form-field class="search-bar-wrapper">
                  <mat-select [(ngModel)]="plan.planSummary.instrumentId">
                    <mat-option [value]="all.ALL">All Instrument</mat-option>
                    <mat-option *ngFor="let instrument of instruments" [value]="instrument.instrumentDetail.id">
                      {{ instrument.instrumentDetail.name }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            }
          </div>
          <div class="plan-content">
            <div class="text-gray">
              {{ planSummaryService.getTotalVisits(plan.planSummary.plandetails.items) }} visits per week
            </div>
            <div class="dot"></div>
            <div class="text-gray">{{ plan.planSummary.plan === plansEnum.RECURRING ? "Recurring" : "Custom" }} plan</div>
          </div>
        </div>
        <div class="plan-pricing">
          <div class="fw-bold">${{ plan.planSummary.planPrice }} / <span class="text-gray">Month</span></div>
          <button
            mat-raised-button
            color="primary"
            class="mat-primary-btn"
            type="button"
            [appLoader]="showBtnLoader"
            (click)="onAssignPlan(plan.planSummary)">
            Assign Plan
          </button>
        </div>
      </div>
    }
  }
  <div *ngIf="!plans?.length">
    <div class="accordion-item fw-bold">No Plans Available.</div>
  </div>
</ng-template>

<ng-template #assignedProductAccordion>
  @for (product of studentProducts; track $index) {
    <div class="accordion-item pointer" *ngIf="studentProducts?.length" (click)="toggleCartItem(true, product)">
      <div class="product-wrapper">
        <div class="plan-name">Order #{{ product.cartId }}</div>
        <div class="plan-content">
          <div class="text-gray">Purchased on
            <span class="primary-color">{{ product.purchaseDate | localDate | date }}</span>
          </div>
          <div class="dot"></div>
          <div class="text-gray">For
            <span class="primary-color">{{ product.studentName }}</span>
          </div>
          <div class="dot"></div>
          <div class="text-gray">{{ transactionTypes[product.transactionType] | titlecase }}</div>
        </div>
      </div>
      <div class="plan-pricing">
        <div class="fw-bold">${{ product.paidAmount }}</div>
        <div class="text-gray">{{ product.cartItems.length }} products</div>
      </div>
    </div>
  }
  <div *ngIf="!studentProducts?.length">
    <div class="accordion-item fw-bold">No Assigned Product</div>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
