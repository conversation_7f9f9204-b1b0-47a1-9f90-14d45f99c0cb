import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { CBResponse, MatDialogRes } from 'src/app/shared/models';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSidenavModule } from '@angular/material/sidenav';
import { Debounce } from 'src/app/shared/decorators';
import { NgxPaginationModule } from 'ngx-pagination';
import { ActivatedRoute, Router } from '@angular/router';
import { DocumentDetails, DocumentInfo, DocumentTypes } from './models';
import { DocumentService } from './services/document.service';
import { AddNewDocumentComponent } from './pages/add-new-document/add-new-document.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { AppToasterService } from 'src/app/shared/services';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    MatIconModule,
    MatInputModule,
    MatSidenavModule,
    NgxPaginationModule,
    CommonModule,
    SharedModule
  ],
  COMPONENTS: [AddNewDocumentComponent]
};

@Component({
  selector: 'app-document',
  standalone: true,
  templateUrl: './document.component.html',
  styleUrl: './document.component.scss',
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS]
})
export class DocumentComponent extends BaseComponent implements OnInit {
  searchTerm!: string;
  totalCount!: number;
  isAddNewDocSideNavOpen!: boolean;

  documentDetails!: DocumentDetails[];
  documentType!: DocumentTypes;

  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  pageTabOptions = { MAIN_DOC: 'Main Document', RENTAL_INSTRUMENT_DOC: 'Rental Instrument Document' };

  selectedTabOption = this.pageTabOptions.MAIN_DOC;
  selectedDocDetails!: DocumentInfo | null;

  constructor(
    private readonly documentService: DocumentService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabOption(this.pageTabOptions.MAIN_DOC);
    this.setActiveTabFromQueryParams();
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      this.selectedTabOption = params.activeTab;
      if (this.selectedTabOption) {
        this.setDocumentType();
      }
    });
  }

  setDocumentType(): void {
    this.documentType =
      this.selectedTabOption === this.pageTabOptions.MAIN_DOC
        ? DocumentTypes.MAIN_DOC
        : DocumentTypes.RENTAL_INSTRUMENT_DOC;
    this.getDocumentDetails(this.documentType, this.currentPage, this.pageSize);
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getDocumentDetails(this.documentType, this.currentPage, this.pageSize);
  }

  getDocumentDetails(documentType: number, currentPage: number, pageSize: number, searchName?: string): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    const nameFilter = searchName ? `?NameFilter=${searchName}` : '';
    this.documentService
      .getListWithFiltersWithPagination<CBResponse<DocumentDetails>>(
        { DocumentType: documentType },
        currentPage,
        pageSize,
        `${API_URL.crud.getAll}${nameFilter}`,
        !!searchName
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<DocumentDetails>) => {
          this.documentDetails = res.result.items;
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  toggleEditDocument(DocDetails: DocumentInfo | null, isOpen: boolean): void {
    this.selectedDocDetails = DocDetails as DocumentInfo | null;
    this.isAddNewDocSideNavOpen = isOpen;
  }

  deleteDocumentConfirmation(docId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Document`,
        message: `Are you sure you want to delete this Document?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteDocument(docId);
      }
    });
  }

  deleteDocument(docId: number): void {
    this.documentService
      .delete(docId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.setDocumentType();
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Document'));
          this.cdr.detectChanges();
        }
      });
  }

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.settings.root, this.path.settings.document], {
      queryParams: {
        activeTab: tabName
      }
    });
  }

  @Debounce(300)
  onSearchTermChanged(): void {
    this.currentPage = 1;
    this.getDocumentDetails(this.documentType, this.currentPage, this.pageSize, this.searchTerm);
  }
}
