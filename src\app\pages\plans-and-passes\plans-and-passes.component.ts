import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { StudentPlanService } from '../scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { CBGetResponse, MatDialogRes } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { StudentPlans } from '../scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { EnumToKeyValuePipe } from 'src/app/shared/pipe';
import { PassStatus, PlanAndPassFilter, PlanProgressStatus, PlansAndPasses, PlanStatus, ActivePlans } from './models';
import { PlanSummaryService } from '../settings/pages/plan/services';
import { All, Duration, Plan, PlanType, VisitsPerWeek } from '../settings/pages/plan/models';
import { PassInfo, PassType } from '../settings/pages/passes/models';
import moment from 'moment';
import { QuitPlanRequestComponent } from './pages/quit-plan-request/quit-plan-request.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { PlanCancelRequestService } from './services';
import { AppToasterService } from 'src/app/shared/services';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    MatSidenavModule,
    MatSelectModule,
    MatButtonModule,
    CommonModule,
    MatFormFieldModule,
    MatIconModule,
    FormsModule,
    MatInputModule,
    ReactiveFormsModule
  ],
  PIPES: [EnumToKeyValuePipe],
  COMPONENTS: [QuitPlanRequestComponent]
};

@Component({
  selector: 'app-plans-and-passes',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './plans-and-passes.component.html',
  styleUrl: './plans-and-passes.component.scss'
})
export class PlansAndPassesComponent extends BaseComponent implements OnInit {
  pageTabOptions = { PLAN: 'Plan', PASS: 'Pass' };
  selectedTabOption = this.pageTabOptions.PLAN;

  planAndPasses!: PlansAndPasses;
  filteredPlans!: Array<StudentPlans>;
  filteredPasses!: Array<PassInfo>;

  all = All;
  plans = Plan;
  durations = Duration;
  visits = VisitsPerWeek;
  passTypes = PassType;
  planTypes = PlanType;
  passStatuses = PassStatus;
  planStatuses = PlanStatus;
  planProgressStatus = PlanProgressStatus;
  activePlans = ActivePlans;

  isSideNavOpen = false;
  selectedPlanDetail!: StudentPlans | null;

  filters: PlanAndPassFilter = {
    durationFilter: 0,
    visitsPerWeekFilter: 0,
    planFilter: 0,
    planTypeFilter: 0,
    passTypeFilter: 0,
    planActiveFilter: 0,
    passStatusFilter: this.passStatuses.ACTIVE
  };

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly studentPlanService: StudentPlanService,
    protected readonly planSummaryService: PlanSummaryService,
    private readonly cdr: ChangeDetectorRef,
    private readonly dialog: MatDialog,
    private readonly planCancelRequestService: PlanCancelRequestService,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabFromQueryParams();
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      const { activeTab, activePlan } = params;

      this.selectedTabOption = 
        activeTab && Object.values(this.pageTabOptions).includes(activeTab)
          ? activeTab
          : this.pageTabOptions.PLAN;

      if (activePlan) {
        this.filters.planActiveFilter = ActivePlans.ACTIVE;
      } else {
        this.clearFilters();
      }

      this.getPlanAndPassesDetail();
    });
  }

  clearFilters(): void {
    this.filters.durationFilter = 0;
    this.filters.visitsPerWeekFilter = 0;
    this.filters.passTypeFilter = 0;
    this.filters.planFilter = 0;
    this.filters.planTypeFilter = 0;
    this.filters.planActiveFilter = 0;
    this.filters.passStatusFilter = this.passStatuses.ACTIVE;
  }

  getPlanAndPassesDetail(): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    this.studentPlanService
      .getList<CBGetResponse<PlansAndPasses>>(`${API_URL.studentPlans.getStudentPlansAndPasses}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<PlansAndPasses>) => {
          this.planAndPasses = res.result;
          this.applyPlanFilters();
          this.applyPassFilters();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  checkActivePlan(plan: StudentPlans, activeFilter: number): boolean {
    switch (activeFilter) {
      case All.ALL:
        return true;
      case ActivePlans.ACTIVE:
        return (
          plan.studentDiscontinuedPlanDetails?.planCancelRequestStatus === PlanStatus.CANCELED ||
          plan.studentDiscontinuedPlanDetails === null
        );
      case ActivePlans.DISCONTINUED:
        return !(
          plan.studentDiscontinuedPlanDetails?.planCancelRequestStatus === PlanStatus.CANCELED ||
          plan.studentDiscontinuedPlanDetails === null
        );
      default:
        return false;
    }
  }

  applyPlanFilters(): void {
    this.filteredPlans = this.planAndPasses.plans.filter((plan) => {
      const durationFilter =
        this.filters.durationFilter === All.ALL ||
        plan.planDetails.some((detail) => detail.planDetail.duration === this.filters.durationFilter);

      const visitsPerWeekFilter =
        this.filters.visitsPerWeekFilter === All.ALL ||
        this.planSummaryService.getTotalVisits(plan.planDetails) === this.filters.visitsPerWeekFilter;

      const planTypeFilter = this.filters.planTypeFilter === All.ALL || plan.planType === this.filters.planTypeFilter;

      const planFilter = this.filters.planFilter === All.ALL || plan.plan === this.filters.planFilter;

      const activeFilter = this.checkActivePlan(plan, this.filters.planActiveFilter);

      return durationFilter && visitsPerWeekFilter && planTypeFilter && planFilter && activeFilter;
    });
  }

  applyPassFilters(): void {
    const { durationFilter, visitsPerWeekFilter, passTypeFilter, passStatusFilter } = this.filters;

    this.filteredPasses = this.planAndPasses.passes.filter((pass) => {
      const passStatus = pass.isUsed
        ? PassStatus.USED
        : this.checkIfPassIsExpired(pass.expiryDate) && !pass.isUsed
          ? PassStatus.EXPIRED
          : PassStatus.ACTIVE;
      return (
        (durationFilter === All.ALL || pass.duration === durationFilter) &&
        (visitsPerWeekFilter === All.ALL || pass.visits === visitsPerWeekFilter) &&
        (passTypeFilter === All.ALL || pass.passType === passTypeFilter) &&
        (passStatusFilter === PassStatus.ACTIVE ? passStatus === PassStatus.ACTIVE : passStatus === passStatusFilter)
      );
    });
  }

  toggleSideNav(isOpen: boolean, selectedPlanDetail: StudentPlans | null): void {
    this.isSideNavOpen = isOpen;
    this.selectedPlanDetail = selectedPlanDetail;
  }

  openCancelRequestConfirmation(id: number | undefined): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Cancel Request`,
        message: `Are you sure you want to cancel this request?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.cancelQuitRequest(id);
      }
    });
  }

  cancelQuitRequest(id: number | undefined): void {
    this.planCancelRequestService
      .add({ id: id }, API_URL.planCancelRequest.cancelRequest)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getPlanAndPassesDetail();
          this.toasterService.success(
            this.constants.successMessages.canceledSuccessfully.replace('{item}', 'Quit plan request')
          );
        }
      });
  }

  checkIfPassIsExpired(expiryDate: string): boolean {
    return moment(expiryDate).isBefore(moment(), 'day');
  }

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.plansAndPasses.root], {
      queryParams: {
        activeTab: tabName
      }
    });
  }

  navigateToMakeUpLesson(pass: PassInfo): void {
    this.router.navigate([this.path.plansAndPasses.root, this.path.scheduleMakeUpLesson], {
      queryParams: { scheduleId: pass.scheduleLessonId, d: pass.duration }
    });
  }

  keepOriginalOrder = () => 0;
}
