import { CommonModule, DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, Output, ChangeDetectorRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { FilterByTitlePipe } from 'src/app/shared/pipe/filter-by-title.pipe';
import { SharedModule } from 'src/app/shared/shared.module';
import { MaintenanceRequests, MaintenanceRequestsState, MaintenanceRequestsStateLabel, OpenOrCloseMaintenanceRequest } from '../../models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { AddEditMaintenanceRequestComponent } from '../add-edit-maintenance-request/add-edit-maintenance-request.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MaintenanceRequestService } from '../../services/maintenance-request.service';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { ViewMaintenanceRequestComponent } from '../view-maintenance-request/view-maintenance-request.component';
import { UpdateMaintenanceRequestStatusComponent } from '../update-maintenance-request-status/update-maintenance-request-status.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AppToasterService } from 'src/app/shared/services';
import { MatDialogRes } from 'src/app/shared/models';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatSidenavModule,
    MatTooltipModule
  ],
  PIPES: [FilterByTitlePipe],
  COMPONENTS: [AddEditMaintenanceRequestComponent, ViewMaintenanceRequestComponent, UpdateMaintenanceRequestStatusComponent]
};

@Component({
  selector: 'app-open-maintenance-requests',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './open-maintenance-requests.component.html',
  styleUrl: './open-maintenance-requests.component.scss'
})
export class OpenMaintenanceRequestsComponent extends BaseComponent {
  @Input() maintenanceRequests!: MaintenanceRequests | undefined;
  @Input() override showPageLoader!: boolean;

  isEditMaintenanceRequest!: boolean;
  isViewMaintenanceRequest!: boolean;
  isUpdateMaintenanceRequest!: boolean;
  selectedMaintenanceRequestDetails!: OpenOrCloseMaintenanceRequest;

  maintenanceRequestsStatus = MaintenanceRequestsState;
  maintenanceRequestsStatusLabel = MaintenanceRequestsStateLabel;
  searchTerm = '';

  @Output() updateMaintenanceRequestList = new EventEmitter<void>();

  constructor(
    private readonly dialog: MatDialog,
    private readonly maintenanceRequestsService: MaintenanceRequestService,
    private readonly datePipe: DatePipe,
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  openEditMaintenanceModal(selectedMaintenanceRequestDetails: OpenOrCloseMaintenanceRequest): void {
    this.selectedMaintenanceRequestDetails = selectedMaintenanceRequestDetails;
    this.isEditMaintenanceRequest = true;
  }

  openViewMaintenanceModal(selectedMaintenanceRequestDetails: OpenOrCloseMaintenanceRequest): void {
    this.selectedMaintenanceRequestDetails = selectedMaintenanceRequestDetails;
    this.isViewMaintenanceRequest = true;
  }

  openUpdateMaintenanceRequestModal(selectedMaintenanceRequestDetails: OpenOrCloseMaintenanceRequest): void {
    this.selectedMaintenanceRequestDetails = selectedMaintenanceRequestDetails;
    this.isUpdateMaintenanceRequest = true;
  }

  deleteMaintenanceRequestConfirmation(maintenanceRequestId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Maintenance Request`,
        message: `Are you sure you want to delete this maintenance request?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteMaintenanceRequest(maintenanceRequestId);
      }
    });
  }

  deleteMaintenanceRequest(maintenanceRequestId: number): void {
    this.maintenanceRequestsService
      .delete(maintenanceRequestId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.updateMaintenanceRequestList.emit();
          this.cdr.detectChanges();
        }
      });
  }

  closeMaintenanceRequestConfirmation(maintenanceRequestId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Close Maintenance Request`,
        message: `Are you sure you want to close this maintenance request?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.closeMaintenanceRequest(maintenanceRequestId);
      }
    });
  }

  closeMaintenanceRequest(maintenanceRequestId: number): void {
    const maintenanceRequestParams = {
      closedOn: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss),
      id: maintenanceRequestId,
      requestStatus: this.maintenanceRequestsStatus.Closed
    };
    this.maintenanceRequestsService
      .update(maintenanceRequestParams, API_URL.maintenanceRequests.updateRequestStatus)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.updateMaintenanceRequestList.emit();
          this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Maintenance request'));
          this.isViewMaintenanceRequest = false;
          this.cdr.detectChanges();
        },
        error: () => {}
      });
  }
}
