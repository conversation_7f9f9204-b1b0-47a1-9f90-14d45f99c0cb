import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse, MatDialogRes } from 'src/app/shared/models';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { ClassTypes, InstructorAvaibility, SuggestedTimeSlot } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';

import { RevenueCategory } from '../../../revenue-categories/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { RoomService } from 'src/app/pages/room-and-location-management/pages/room/services';
import { RoomDetails } from 'src/app/pages/room-and-location-management/models';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import moment from 'moment';
import {
  AssignedInstructors,
  EnsembleClassScheduleSummaryInfo,
  EnsembleClassView,
  UpdateEnsembleClassFormGroup
} from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { EnsembleClassesService } from 'src/app/pages/schedule-classes/pages/ensemble-class/services';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonUtils } from 'src/app/shared/utils';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    CommonModule,
    SharedModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatSelectModule,
    MatTooltipModule
  ]
};

@Component({
  selector: 'app-update-ensemble-class',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './update-ensemble-class.component.html',
  styleUrl: './update-ensemble-class.component.scss'
})
export class UpdateEnsembleClassComponent extends BaseComponent implements OnInit {
  @Input() selectedEnsembleId!: number | null;
  @Input() isCloneSideNavOpen!: boolean;

  ensembleClassDetail!: EnsembleClassScheduleSummaryInfo | undefined;
  ensembleClassView!: EnsembleClassView | undefined;
  updateEnsembleClassForm!: FormGroup<UpdateEnsembleClassFormGroup>;
  suggestedTimeSlots!: SuggestedTimeSlot[] | undefined;
  revenueCategories!: Array<RevenueCategory>;
  rooms!: Array<RoomDetails>;

  selectedTimeSlot!: string | null;
  classTypes = ClassTypes;
  maxDate = new Date();
  transformedDate = this.datePipe.transform(this.maxDate, this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss);

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() isEnsembleClassUpdated = new EventEmitter<void>();

  constructor(
    protected readonly schedulerService: SchedulerService,
    private readonly ensembleClassService: EnsembleClassesService,
    private readonly cdr: ChangeDetectorRef,
    private readonly datePipe: DatePipe,
    private readonly commonService: CommonService,
    private readonly roomService: RoomService,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getEnsembleClassDetail(this.selectedEnsembleId);
    this.getRevenueCategories();
    this.initUpdateEnsembleClassForm();
  }

  initUpdateEnsembleClassForm(): void {
    this.updateEnsembleClassForm = new FormGroup<UpdateEnsembleClassFormGroup>({
      id: new FormControl(undefined, { nonNullable: true }),
      description: new FormControl('', { nonNullable: true }),
      ensembleClassName: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      studentCapacity: new FormControl(this.constants.defaultStudentCapacity, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(1)]
      }),
      scheduleStartDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndDate: new FormControl('', { nonNullable: true }),
      enrollLastDate: new FormControl('', { nonNullable: true }),
      daysOfSchedule: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleStartTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      price: new FormControl(undefined, { nonNullable: true }),
      categoryId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] })
    });
  }

  getEnsembleClassDetail(id: number | null): void {
    this.showPageLoader = true;
    this.ensembleClassService
      .get<CBGetResponse<EnsembleClassScheduleSummaryInfo>>(
        `${API_URL.ensembleClassScheduleSummaries.getEnsembleClassesScheduleSummaryForView}?id=${id}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<EnsembleClassScheduleSummaryInfo>) => {
          this.ensembleClassDetail = res.result;
          this.isCloneSideNavOpen ? this.setFormFieldForCloneEnsembleClass() : this.setEnsembleClassFormData();
          this.getSuggestedTime(false);
          this.getRooms();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getRevenueCategories(): void {
    this.commonService
      .getRevenueCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RevenueCategory>) => {
          this.revenueCategories = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParamsForRooms() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      page: 1,
      locationIdFilter: [this.ensembleClassDetail?.locationId ?? 0]
    });
  }

  getRooms(): void {
    this.roomService
      .add(this.getFilterParamsForRooms(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RoomDetails>) => {
          this.rooms = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  setEnsembleClassFormData(): void {
    const ensembleClassInfo = this.ensembleClassDetail;
    this.updateEnsembleClassForm.patchValue({ ...ensembleClassInfo, daysOfSchedule: [Number(ensembleClassInfo?.daysOfSchedule)] });
    this.selectedTimeSlot = `${this.datePipe.transform(ensembleClassInfo?.scheduleStartTime, 'shortTime')} - ${this.datePipe.transform(
      ensembleClassInfo?.scheduleEndTime,
      'shortTime'
    )}`;
  }

  setFormFieldForCloneEnsembleClass(): void {
    const ensembleClassInfo = this.ensembleClassDetail;
    this.updateEnsembleClassForm.patchValue({ ...ensembleClassInfo });
    this.setFormControlValue('daysOfSchedule', '');
    this.setFormControlValue('id', null);
    this.setFormControlValue('scheduleStartTime', '');
    this.setFormControlValue('scheduleEndTime', '');
    this.setFormControlValue('scheduleStartDate', '');
    this.setFormControlValue('scheduleEndDate', '');
    this.setFormControlValue('enrollLastDate', '');
  }

  setEnrollDate(): void {
    const scheduleStartDate = moment(this.updateEnsembleClassForm.getRawValue().scheduleStartDate);
    const today = moment().startOf('day');
    const twoDaysBefore =
      scheduleStartDate.diff(today, 'days') > 1 ? scheduleStartDate.subtract(2, 'days').toDate() : scheduleStartDate.toDate();
    this.setFormControlValue('enrollLastDate', twoDaysBefore);
  }

  getDayOfWeek(): void {
    const day = moment(this.updateEnsembleClassForm.controls.scheduleStartDate.value).day();
    this.setFormControlValue('daysOfSchedule', day);
  }

  getInstrumentNames(array: AssignedInstructors[]) {
    return array
      .slice(1)
      .map(item => item.name)
      .join(', ');
  }

  get getInstructorAvailability(): InstructorAvaibility {
    return {
      classType: this.classTypes.ENSEMBLE_CLASS,
      scheduleStartDate: this.datePipe.transform(
        this.updateEnsembleClassForm.controls.scheduleStartDate.value,
        this.constants.dateFormats.yyyy_MM_dd
      ),
      scheduleEndDate: this.datePipe.transform(
        this.updateEnsembleClassForm.controls.scheduleStartDate.value,
        this.constants.dateFormats.yyyy_MM_dd
      ),
      daysOfSchedule: [Number(this.updateEnsembleClassForm.controls.daysOfSchedule.value)],
      isAllInstances: true,
      instructorIds: this.ensembleClassDetail?.assignedInstructors.map(instructor => instructor.id),
      planId: null,
      duration: this.ensembleClassDetail?.duration ?? null,
      locationId: this.ensembleClassDetail?.locationId
    };
  }

  getSuggestedTime(clearScheduleDate: boolean): void {
    if (this.updateEnsembleClassForm.controls.scheduleStartDate.value) {
      this.ensembleClassService
        .add(this.getInstructorAvailability, API_URL.ensembleClassesScheduleSummaries.getInstructorAvaibilableTimeSlots)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<SuggestedTimeSlot[]>) => {
            this.suggestedTimeSlots = response.result;
            if (clearScheduleDate) {
              this.selectedTimeSlot = null;
              this.updateEnsembleClassForm.controls.scheduleStartTime.reset();
            }
            this.cdr.detectChanges();
          }
        });
    }
  }

  clearScheduleEndDate(): void {
    this.updateEnsembleClassForm.controls.scheduleEndDate.reset();
  }

  setStartAndEndTime(selectedTimeSlot: SuggestedTimeSlot): void {
    this.selectedTimeSlot = `${selectedTimeSlot.startTime} - ${selectedTimeSlot.endTime}`;
    this.updateEnsembleClassForm.patchValue({
      scheduleStartTime: selectedTimeSlot.startTime,
      scheduleEndTime: selectedTimeSlot.endTime
    });
  }

  checkCapacity(): void {
    if (this.updateEnsembleClassForm.invalid) {
      this.updateEnsembleClassForm.markAllAsTouched();
      return;
    }
    const selectedRoomId = this.ensembleClassDetail?.roomId;
    const selectedRoom = this.rooms?.find(room => room.roomDetail.id === selectedRoomId);
    const formCapacity = this.updateEnsembleClassForm.getRawValue().studentCapacity;

    if (formCapacity > +selectedRoom?.roomDetail.capacity!) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Alert',
          message: `Selected room capacity is ${selectedRoom?.roomDetail.capacity} which is is less than the required capacity.`,
          acceptBtnName: `${this.isCloneSideNavOpen ? 'Clone ' : 'Update '} Anyway`,
          rejectBtnName: 'Change'
        }
      });

      dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
        if (result.isConfirmed) {
          this.isCloneSideNavOpen ? this.onCloneEnsembleClass() : this.onUpdateEnsembleClass();
        }
      });
      return;
    }
    this.isCloneSideNavOpen ? this.onCloneEnsembleClass() : this.onUpdateEnsembleClass();
  }

  onUpdateEnsembleClass(): void {
    this.updateEnsembleClassForm.markAsUntouched();
    this.showBtnLoader = true;
    this.cdr.detectChanges();
    this.ensembleClassService
      .update(
        {
          ...this.updateEnsembleClassForm.getRawValue(),
          scheduleStartDate: this.datePipe.transform(
            new Date(this.updateEnsembleClassForm.getRawValue().scheduleStartDate),
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleEndDate: this.datePipe.transform(
            this.updateEnsembleClassForm.getRawValue().scheduleStartDate,
            this.constants.dateFormats.yyyy_MM_dd
          ),
          enrollLastDate: this.datePipe.transform(
            this.updateEnsembleClassForm.getRawValue().scheduleStartDate,
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleStartTime: this.datePipe.transform(
            this.updateEnsembleClassForm.getRawValue().scheduleStartTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          scheduleEndTime: this.datePipe.transform(
            this.updateEnsembleClassForm.getRawValue().scheduleEndTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          daysOfSchedule: [Number(this.updateEnsembleClassForm.getRawValue().daysOfSchedule)]
        },
        API_URL.crud.update
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.isEnsembleClassUpdated.emit();
          this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Ensemble Class'));
          this.onCloseModal();
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onCloneEnsembleClass(): void {
    this.updateEnsembleClassForm.markAsUntouched();
    this.showBtnLoader = true;
    this.cdr.detectChanges();
    this.ensembleClassService
      .add(
        {
          ...this.updateEnsembleClassForm.getRawValue(),
          ...this.ensembleClassDetail,
          assignedInstructors: this.ensembleClassDetail?.assignedInstructors.map(instructor => instructor.id),
          assignedInstruments: this.ensembleClassDetail?.assignedInstruments.map(instruments => instruments.instrumentId),
          scheduleStartDate: this.datePipe.transform(
            new Date(this.updateEnsembleClassForm.getRawValue().scheduleStartDate),
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleEndDate: this.datePipe.transform(
            this.updateEnsembleClassForm.getRawValue().scheduleStartDate,
            this.constants.dateFormats.yyyy_MM_dd
          ),
          enrollLastDate: this.datePipe.transform(
            this.updateEnsembleClassForm.getRawValue().scheduleStartDate,
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleStartTime: this.datePipe.transform(
            this.updateEnsembleClassForm.getRawValue().scheduleStartTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          scheduleEndTime: this.datePipe.transform(
            this.updateEnsembleClassForm.getRawValue().scheduleEndTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          daysOfSchedule: [Number(this.updateEnsembleClassForm.getRawValue().daysOfSchedule)],
          id: null
        },
        API_URL.crud.create
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showBtnLoader = false;
          this.toasterService.success(this.constants.successMessages.clonedSuccessfully.replace('{item}', 'Ensemble Class'));
          this.onCloseModal();
          this.isEnsembleClassUpdated.emit();
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  setFormControlValue(controlName: string, value: number | string | boolean | null | Date): void {
    (this.updateEnsembleClassForm.controls as any)[controlName].setValue(value);
    if (controlName === 'daysOfSchedule') {
      this.getSuggestedTime(true);
    }
  }

  onCloseModal(): void {
    this.updateEnsembleClassForm.reset();
    this.closeSideNav.emit();
  }
}
