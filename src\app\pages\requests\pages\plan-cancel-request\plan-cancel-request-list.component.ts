import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSidenavModule } from '@angular/material/sidenav';
import { NgxPaginationModule } from 'ngx-pagination';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { Debounce } from 'src/app/shared/decorators';
import { CBResponse, MatDialogRes } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CommonUtils } from 'src/app/shared/utils';
import { MatSelectModule } from '@angular/material/select';
import { DashIfEmptyPipe, EnumToKeyValuePipe } from 'src/app/shared/pipe';
import { AppToasterService } from 'src/app/shared/services';
import { All, Duration, PlanFilters, PlanType, VisitsPerWeek } from 'src/app/pages/settings/pages/plan/models';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { PlanCancelRequestService } from 'src/app/pages/plans-and-passes/services';
import { StudentPlans } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { PlanStatus } from 'src/app/pages/plans-and-passes/models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { CancelRequestSidenavComponent } from './pages/cancel-request-sidenav/cancel-request-sidenav.component';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    MatFormFieldModule,
    FormsModule,
    MatIconModule,
    MatInputModule,
    MatSidenavModule,
    NgxPaginationModule,
    CommonModule,
    SharedModule,
    MatSelectModule,
    MatTooltipModule
  ],
  COMPONENTS: [CancelRequestSidenavComponent],
  PIPES: [EnumToKeyValuePipe, DashIfEmptyPipe]
};

@Component({
  selector: 'app-plan-cancel-request',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './plan-cancel-request-list.component.html',
  styleUrl: './plan-cancel-request-list.component.scss'
})
export class PlanCancelRequestListComponent extends BaseComponent implements OnInit {
  totalCount!: number;
  cancelPlanRequests!: Array<StudentPlans>;
  all = All;
  planTypes = PlanType;
  visits = VisitsPerWeek;
  durations = Duration;
  planCancelStatus = PlanStatus;

  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  isViewSideNavOpen = false;
  selectedRequestId!: number | undefined;

  filters: PlanFilters = {
    planFilter: All.ALL,
    planTypeFilter: All.ALL,
    DurationFilter: All.ALL,
    VisitsPerWeekFilter: All.ALL
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    protected readonly planSummaryService: PlanSummaryService,
    private readonly planCancelRequestService: PlanCancelRequestService,
    private readonly toasterService: AppToasterService,
    private readonly dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCancelPlanRequest(this.currentPage, this.pageSize);
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getCancelPlanRequest(this.currentPage, this.pageSize);
  }

  getFilterParams(currentPage: number, pageSize: number) {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      Duration: this.filters.DurationFilter,
      Visits: this.filters.VisitsPerWeekFilter,
      PlanType: this.filters.planTypeFilter,
      page: currentPage,
      pageSize: pageSize
    });
  }

  getCancelPlanRequest(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();

    this.planCancelRequestService
      .getListWithFilters<CBResponse<StudentPlans>>(this.getFilterParams(currentPage, pageSize), API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentPlans>) => {
          this.setLocalTime(res);
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  setLocalTime(res: CBResponse<StudentPlans>) {
    this.cancelPlanRequests = res.result.items.map((event: StudentPlans) => {
      const discontinuedPlanDetails = event.studentDiscontinuedPlanDetails
        ? {
            ...event.studentDiscontinuedPlanDetails,
            localApprovedDate: DateUtils.toLocal(
              event.studentDiscontinuedPlanDetails.approvedDate,
              'yyyy-MM-DDTHH:mm:ss.SSS+0000',
              'MM/DD/YYYY hh:mm A'
            ),
            localRequestDate: DateUtils.toLocal(
              event.studentDiscontinuedPlanDetails.requestDate,
              'yyyy-MM-DDTHH:mm:ss.SSS+0000',
              'MM/DD/YYYY hh:mm A'
            ),
            localCanceledDate: DateUtils.toLocal(
              event.studentDiscontinuedPlanDetails.canceledDate,
              'yyyy-MM-DDTHH:mm:ss.SSS+0000',
              'MM/DD/YYYY hh:mm A'
            )
          }
        : null;

      return {
        ...event,
        studentDiscontinuedPlanDetails: discontinuedPlanDetails
      };
    });
  }

  approveRequestConfirmation(id: number | undefined): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Approve Plan Cancel Request`,
        message: `Are you sure you want to approve this request?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.approveRequest(id);
      }
    });
  }

  approveRequest(id: number | undefined): void {
    this.planCancelRequestService
      .add('', `${API_URL.planCancelRequest.approveRequest}?id=${id}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.getCancelPlanRequest(this.currentPage, this.pageSize);
          this.toasterService.success(
            this.constants.successMessages.approvedSuccessfully.replace('{item}', 'Plan quit request')
          );
          this.cdr.detectChanges();
        }
      });
  }

  toggleSideNav(isOpen: boolean, id: number | undefined): void {
    this.isViewSideNavOpen = isOpen;
    this.selectedRequestId = id;
  }

  getInitials(firstName: string, lastName: string): string {
    return CommonUtils.getInitials(firstName, lastName);
  }

  @Debounce(300)
  onSearchTermChanged(): void {
    this.currentPage = 1;
    this.getCancelPlanRequest(this.currentPage, this.pageSize);
  }
}
