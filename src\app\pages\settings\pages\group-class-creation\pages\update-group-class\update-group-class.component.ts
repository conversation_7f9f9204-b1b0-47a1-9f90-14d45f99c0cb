import { CommonModule, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse } from 'src/app/shared/models';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import {
  ClassTypes,
  InstructorAvaibility,
  SuggestedTimeSlot
} from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { GroupClassesService } from 'src/app/pages/schedule-classes/pages/group-class/services';
import {
  GroupClassView,
  UpdateGroupClassFormGroup
} from 'src/app/pages/schedule-classes/pages/group-class/models/group-class.model';
import { RevenueCategory } from '../../../revenue-categories/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { RoomService } from 'src/app/pages/room-and-location-management/pages/room/services';
import { RoomDetails } from 'src/app/pages/room-and-location-management/models';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import moment from 'moment';
import { CommonUtils } from 'src/app/shared/utils';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    CommonModule,
    SharedModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatSelectModule
  ]
};

@Component({
  selector: 'app-update-group-class',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './update-group-class.component.html',
  styleUrl: './update-group-class.component.scss'
})
export class UpdateGroupClassComponent extends BaseComponent implements OnInit {
  @Input() selectedGroupId!: number | null;
  @Input() isCloneSideNavOpen!: boolean;

  groupClassDetail!: GroupClassView | undefined;
  updateGroupClassForm!: FormGroup<UpdateGroupClassFormGroup>;
  suggestedTimeSlots!: SuggestedTimeSlot[] | undefined;
  revenueCategories!: Array<RevenueCategory>;
  rooms!: Array<RoomDetails>;

  selectedTimeSlot!: string | null;
  classTypes = ClassTypes;
  maxDate = new Date();
  transformedDate = this.datePipe.transform(this.maxDate, this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss);

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() isGroupClassUpdated = new EventEmitter<void>();

  constructor(
    protected readonly schedulerService: SchedulerService,
    private readonly groupClassService: GroupClassesService,
    private readonly cdr: ChangeDetectorRef,
    private readonly datePipe: DatePipe,
    private readonly commonService: CommonService,
    private readonly roomService: RoomService,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getGroupClassDetail(this.selectedGroupId);
    this.getRevenueCategories();
    this.initUpdateGroupClassForm();
  }

  initUpdateGroupClassForm(): void {
    this.updateGroupClassForm = new FormGroup<UpdateGroupClassFormGroup>({
      id: new FormControl(undefined, { nonNullable: true }),
      description: new FormControl('', { nonNullable: true }),
      groupClassName: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      studentCapacity: new FormControl(this.constants.defaultStudentCapacity, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(1)]
      }),
      scheduleStartDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      enrollLastDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      daysOfSchedule: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleStartTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      scheduleEndTime: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      price: new FormControl(undefined, { nonNullable: true, validators: [Validators.required, Validators.min(1)] }),
      categoryId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] })
    });
  }

  getGroupClassDetail(id: number | null): void {
    this.showPageLoader = true;
    this.groupClassService
      .get<CBGetResponse<GroupClassView>>(
        `${API_URL.groupClassScheduleSummaries.getGroupClassScheduleSummaryForView}?id=${id}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<GroupClassView>) => {
          this.groupClassDetail = res.result;
          this.isCloneSideNavOpen ? this.setFormFieldForCloneGroupClass() : this.setGroupClassFormData();
          this.getSuggestedTime(false);
          this.getRooms();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getRevenueCategories(): void {
    this.commonService
      .getRevenueCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RevenueCategory>) => {
          this.revenueCategories = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParamsForRooms() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      page: 1,
      locationIdFilter: [this.groupClassDetail?.groupClassScheduleSummary.locationId ?? 0]
    });
  }

  getRooms(): void {
    this.roomService
      .add(this.getFilterParamsForRooms(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RoomDetails>) => {
          this.rooms = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  setGroupClassFormData(): void {
    const groupClassInfo = this.groupClassDetail?.groupClassScheduleSummary;
    this.updateGroupClassForm.patchValue({ ...groupClassInfo, daysOfSchedule: [Number(groupClassInfo?.scheduleDays)] });
    this.selectedTimeSlot = `${this.datePipe.transform(
      groupClassInfo?.scheduleStartTime,
      'shortTime'
    )} - ${this.datePipe.transform(groupClassInfo?.scheduleEndTime, 'shortTime')}`;
  }

  setFormFieldForCloneGroupClass(): void {
    const groupClassInfo = this.groupClassDetail?.groupClassScheduleSummary;
    this.updateGroupClassForm.patchValue({ ...groupClassInfo });
    this.setFormControlValue('daysOfSchedule', '');
    this.setFormControlValue('id', null);
    this.setFormControlValue('scheduleStartTime', '');
    this.setFormControlValue('scheduleEndTime', '');
    this.setFormControlValue('scheduleStartDate', '');
    this.setFormControlValue('scheduleEndDate', '');
    this.setFormControlValue('enrollLastDate', '');
  }

  setEnrollDate(): void {
    const scheduleStartDate = moment(this.updateGroupClassForm.getRawValue().scheduleStartDate);
    const today = moment().startOf('day');
    const twoDaysBefore =
      scheduleStartDate.diff(today, 'days') > 1
        ? scheduleStartDate.subtract(2, 'days').toDate()
        : scheduleStartDate.toDate();
    this.setFormControlValue('enrollLastDate', twoDaysBefore);
  }

  get getInstructorAvailability(): InstructorAvaibility {
    return {
      classType: this.classTypes.GROUP_CLASS,
      scheduleStartDate: this.datePipe.transform(
        this.updateGroupClassForm.controls.scheduleStartDate.value,
        this.constants.dateFormats.yyyy_MM_dd
      ),
      scheduleEndDate: this.datePipe.transform(
        this.updateGroupClassForm.controls.scheduleEndDate.value,
        this.constants.dateFormats.yyyy_MM_dd
      ),
      daysOfSchedule: [Number(this.updateGroupClassForm.controls.daysOfSchedule.value)],
      isAllInstances: true,
      instructorId: this.groupClassDetail?.groupClassScheduleSummary.instructorId,
      planId: null,
      duration: this.groupClassDetail?.groupClassScheduleSummary.duration ?? null,
      locationId: this.groupClassDetail?.groupClassScheduleSummary.locationId
    };
  }

  getSuggestedTime(clearScheduleDate: boolean): void {
    if (this.updateGroupClassForm.controls.scheduleStartDate.value) {
      this.schedulerService
        .add(this.getInstructorAvailability, API_URL.scheduleLessonDetails.getInstructorAvaibility)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<SuggestedTimeSlot[]>) => {
            this.suggestedTimeSlots = response.result;
            if (clearScheduleDate) {
              this.selectedTimeSlot = null;
              this.updateGroupClassForm.controls.scheduleStartTime.reset();
            }
            this.cdr.detectChanges();
          }
        });
    }
  }

  clearScheduleEndDate(): void {
    this.updateGroupClassForm.controls.scheduleEndDate.reset();
  }

  setStartAndEndTime(selectedTimeSlot: SuggestedTimeSlot): void {
    this.selectedTimeSlot = `${selectedTimeSlot.startTime} - ${selectedTimeSlot.endTime}`;
    this.updateGroupClassForm.patchValue({
      scheduleStartTime: selectedTimeSlot.startTime,
      scheduleEndTime: selectedTimeSlot.endTime
    });
  }

  checkCapacity(): void {
    if (this.updateGroupClassForm.invalid) {
      this.updateGroupClassForm.markAllAsTouched();
      return;
    }
    const selectedRoomId = this.groupClassDetail?.groupClassScheduleSummary.roomId;
    const selectedRoom = this.rooms?.find((room) => room.roomDetail.id === selectedRoomId);
    const formCapacity = this.updateGroupClassForm.getRawValue().studentCapacity;

    if (formCapacity > +selectedRoom?.roomDetail.capacity!) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Alert',
          message: 'Selected room capacity is less than the required capacity.',
          acceptBtnName: `${this.isCloneSideNavOpen ? 'Clone ' : 'Update '} Anyway`,
          rejectBtnName: 'Change'
        }
      });

      dialogRef.afterClosed().subscribe((result) => {
        if (result.isConfirmed) {
          this.isCloneSideNavOpen ? this.onCloneGroupClass() : this.onUpdateGroupClass();
        }
      });
      return;
    }
    this.isCloneSideNavOpen ? this.onCloneGroupClass() : this.onUpdateGroupClass();
  }

  onUpdateGroupClass(): void {
    this.updateGroupClassForm.markAsUntouched();
    this.showBtnLoader = true;
    this.groupClassService
      .update(
        {
          ...this.updateGroupClassForm.getRawValue(),
          scheduleStartDate: this.datePipe.transform(
            new Date(this.updateGroupClassForm.getRawValue().scheduleStartDate),
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleEndDate: this.datePipe.transform(
            this.updateGroupClassForm.getRawValue().scheduleEndDate,
            this.constants.dateFormats.yyyy_MM_dd
          ),
          enrollLastDate: this.datePipe.transform(
            this.updateGroupClassForm.getRawValue().enrollLastDate,
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleStartTime: this.datePipe.transform(
            this.updateGroupClassForm.getRawValue().scheduleStartTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          scheduleEndTime: this.datePipe.transform(
            this.updateGroupClassForm.getRawValue().scheduleEndTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          daysOfSchedule: [Number(this.updateGroupClassForm.getRawValue().daysOfSchedule)]
        },
        API_URL.crud.update
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.isGroupClassUpdated.emit();
          this.onCloseModal();
          this.toasterService.success(
            this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Group Class')
          );
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  onCloneGroupClass(): void {
    this.updateGroupClassForm.markAsUntouched();
    this.showBtnLoader = true;
    this.groupClassService
      .add(
        {
          ...this.updateGroupClassForm.getRawValue(),
          ...this.groupClassDetail?.groupClassScheduleSummary,
          ...this.groupClassDetail?.instructorDetails,
          scheduleStartDate: this.datePipe.transform(
            new Date(this.updateGroupClassForm.getRawValue().scheduleStartDate),
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleEndDate: this.datePipe.transform(
            this.updateGroupClassForm.getRawValue().scheduleEndDate,
            this.constants.dateFormats.yyyy_MM_dd
          ),
          enrollLastDate: this.datePipe.transform(
            this.updateGroupClassForm.getRawValue().enrollLastDate,
            this.constants.dateFormats.yyyy_MM_dd
          ),
          scheduleStartTime: this.datePipe.transform(
            this.updateGroupClassForm.getRawValue().scheduleStartTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          scheduleEndTime: this.datePipe.transform(
            this.updateGroupClassForm.getRawValue().scheduleEndTime,
            this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
          ),
          daysOfSchedule: [Number(this.updateGroupClassForm.getRawValue().daysOfSchedule)]
        },
        API_URL.crud.create
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(
            this.constants.successMessages.clonedSuccessfully.replace('{item}', 'Group Class')
          );
          this.onCloseModal();
          this.isGroupClassUpdated.emit();
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  setFormControlValue(controlName: string, value: number | string | boolean | null | Date): void {
    (this.updateGroupClassForm.controls as any)[controlName].setValue(value);
    if (controlName === 'daysOfSchedule') {
      this.getSuggestedTime(true);
    }
  }

  onCloseModal(): void {
    this.updateGroupClassForm.reset();
    this.closeSideNav.emit();
  }
}
