import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { SignUpForOptions, DependentInfoForm } from 'src/app/auth/models';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { Instrument, RequestInformationFormGroupType, SubInstrument } from '../../models';
import { takeUntil } from 'rxjs';
import { CBResponse } from 'src/app/shared/models';
import { AuthService } from 'src/app/auth/services';
import { provideNativeDateAdapter } from '@angular/material/core';
import { InstrumentsService, RequestInformationService, SubInstrumentsService } from '../../services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { dateOfBirthValidator, phoneNumberValidator } from 'src/app/shared/validators';

const DEPENDENCIES = {
  MODULES: [
    ReactiveFormsModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatSelectModule,
    SharedModule,
    NgxMaskDirective,
    MatDatepickerModule,
    MatButtonModule
  ],
  COMPONENTS: []
};
@Component({
  selector: 'app-request-information-form',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter(), provideNgxMask()],
  templateUrl: './request-information-form.component.html',
  styleUrl: './request-information-form.component.scss'
})
export class RequestInformationFormComponent extends BaseComponent implements OnInit {
  @Input() isRequestInfoLesson = true;

  requestInformationFormGroup!: FormGroup<RequestInformationFormGroupType>;
  locations!: Array<SchoolLocations>;
  instrumentTypes!: Array<Instrument>;
  subInstruments!: Array<SubInstrument>;

  signUpForOptions = SignUpForOptions;
  refFromOptions = this.constants.refFromOptions;
  maxDate = new Date();

  @Output() formSubmitted = new EventEmitter<boolean>();

  constructor(
    private readonly authService: AuthService,
    private readonly instrumentsService: InstrumentsService,
    private readonly cdr: ChangeDetectorRef,
    private readonly dialog: MatDialog,
    private readonly requestInformationService: RequestInformationService,
    private readonly subInstrumentService: SubInstrumentsService
  ) {
    super();
  }
  ngOnInit(): void {
    this.initRequestInformationFormGroup();
    this.getAllLocations();
    this.getInstruments();
    if (!this.isRequestInfoLesson) {
      this.onCheckboxChange(!this.isRequestInfoLesson);
    }
  }

  getAllLocations(): void {
    this.authService
      .getAllLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.showPageLoader = true;
    this.instrumentsService
      .getList<CBResponse<Instrument>>(API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instrumentTypes = res.result.items;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getSubInstruments(): void {
    this.subInstrumentService
      .getList<CBResponse<SubInstrument>>(
        `${API_URL.crud.getAll}?instrumentId=${this.requestInformationFormGroup.controls.instrumentId.value}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SubInstrument>) => {
          this.subInstruments = res.result.items;
          this.setRequiredBasedOnCondition('subInstrumentId', !!res.result.items.length);
          this.cdr.detectChanges();
        }
      });
  }

  onSubmit(): void {
    if (this.requestInformationFormGroup.invalid) {
      this.requestInformationFormGroup.markAllAsTouched();
      return;
    }
    this.showBtnLoader = true;
    this.requestInformationFormGroup.markAsUntouched();
    this.requestInformationService
      .add(this.requestInformationFormGroup.getRawValue(), API_URL.crud.create)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showBtnLoader = false;
          this.formSubmitted.emit(true);
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.formSubmitted.emit(false);
          this.cdr.detectChanges();
        }
      });
  }

  setDependentId(dependentId: number): void {
    this.requestInformationFormGroup.controls.dependentId.setValue(dependentId);
    if (
      !this.requestInformationFormGroup.controls.dependentDetails.length &&
      dependentId !== this.signUpForOptions.YOURSELF
    ) {
      this.addMoreChild();
    } else if (dependentId === this.signUpForOptions.YOURSELF) {
      this.requestInformationFormGroup.controls.dependentDetails.clear();
    }
  }

  setInstrumentId(instrumentId: number): void {
    this.requestInformationFormGroup.controls.instrumentId.setValue(instrumentId);
    this.getSubInstruments();
  }

  setSubInstrumentId(subInstrumentId: number): void {
    this.requestInformationFormGroup.controls.subInstrumentId.setValue(subInstrumentId);
  }

  setAttendingSchool(attendingSchool: boolean): void {
    this.requestInformationFormGroup.controls.isStudentAttendingSchool.setValue(attendingSchool);
  }

  initRequestInformationFormGroup(): void {
    this.requestInformationFormGroup = new FormGroup<RequestInformationFormGroupType>({
      dependentId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      isChildWithSpecialNeeds: new FormControl(!this.isRequestInfoLesson, { nonNullable: true }),
      firstName: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.NAME_PATTERN)]
      }),
      lastName: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.NAME_PATTERN)]
      }),
      emailId: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.EMAIL)]
      }),
      phoneNo: new FormControl('', { nonNullable: true, validators: [Validators.required, phoneNumberValidator()] }),
      dependentDetails: new FormArray<FormGroup<DependentInfoForm>>([]),
      locationId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      instrumentId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
      studentClassification: new FormControl('', { nonNullable: true }),
      physicalBehaviour: new FormControl('', { nonNullable: true }),
      isStudentAttendingSchool: new FormControl(true, { nonNullable: true }),
      typeOfClassroom: new FormControl('', { nonNullable: true }),
      studentLanguageSkills: new FormControl('', { nonNullable: true }),
      goalOfTakingMusicLesson: new FormControl('', { nonNullable: true }),
      referenceFrom: new FormControl('', { nonNullable: true }),
      message: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      dateOfBirth: new FormControl('', { nonNullable: true, validators: [Validators.required, dateOfBirthValidator()] }),
      subInstrumentId: new FormControl(undefined, { nonNullable: true })
    });
  }

  get getDependentInfoFormArray(): FormArray {
    return this.requestInformationFormGroup.get('dependentDetails') as FormArray;
  }

  addMoreChild(): void {
    this.getDependentInfoFormArray.push(
      new FormGroup({
        firstName: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
        lastName: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
        dateOfBirth: new FormControl('', { nonNullable: true }),
        locationId: new FormControl(undefined, { nonNullable: true })
      })
    );
  }

  deleteDependent(dependentIndex: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Dependent Information`,
        message: `Are you sure you want to delete this dependent information?`
      }
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result.isConfirmed) {
        this.getDependentInfoFormArray.removeAt(dependentIndex);
        this.cdr.detectChanges();
      }
    });
  }

  onCheckboxChange(checked: boolean) {
    const isSpecialNeed = checked;
    this.setRequiredBasedOnCondition('studentClassification', isSpecialNeed);
    this.setRequiredBasedOnCondition('physicalBehaviour', isSpecialNeed);
    this.setRequiredBasedOnCondition('isStudentAttendingSchool', isSpecialNeed);
    this.setRequiredBasedOnCondition('typeOfClassroom', isSpecialNeed);
    this.setRequiredBasedOnCondition('studentLanguageSkills', isSpecialNeed);
    this.setRequiredBasedOnCondition('goalOfTakingMusicLesson', isSpecialNeed);
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean): void {
    const control = this.requestInformationFormGroup.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }
}
