import { StudentGradeInfo } from "../../members/pages/students/models";
import { LeaveBalance, LeaveRequestDetails } from "../../requests/pages/leave-request/models";
import { OpenOrCloseMaintenanceRequest } from "../../requests/pages/maintenance-request/models";
import { ScheduleDetailsView, StudentDetail, StudentPlans } from "../../scheduler/pages/scheduler-wrapper/pages/schedule/models";
import { PassInfo } from "../../settings/pages/passes/models";
import { InquiryDetail } from "../../student-inquiry/models";

export interface DashboardData {
  totalNumberOfStaffMember: number;
  studentDetails: TotalNumberOfStudent;
  inquiryDetails: Array<InquiryDetail>;
  leaveRequest: Array<LeaveRequestDetails>;
  memberOnLeave: Array<LeaveRequestDetails>;
  upcomingBirthdays: Array<UpcomingBirthday>;
  studentDiscontinuedPlans: Array<StudentPlans>;
  dashboardMaintenanceRequest: Array<OpenOrCloseMaintenanceRequest>;
  scheduleLessonDetails: Array<ScheduleDetailsView>;
  supervisorScheduleLessonDetails: Array<ScheduleDetailsView>;
  instructorScheduleLessonDetails: Array<ScheduleDetailsView>;
  instructorDailyWorkingHours: Array<InstructorWorkingHours>;
  instructorMonthlyWorkingHours: Array<InstructorWorkingHours>;
  studentGrades: Array<StudentGradeInfo>;
  activePasses: Array<PassInfo> | null;
  activePlans: Array<StudentPlans> | null;
  leaveBalance: Array<LeaveBalance>;
  dailyWorkingHours: WorkingHours;
  mounthlyWorkingHours: WorkingHours;
  studentDuePlan: StudentPlans | null;
}

export interface TotalNumberOfStudent {
  totalNumberOfStudent: number;
}

export interface UpcomingBirthday {
  id: number;
  name: string;
  profilePhoto?: string;
  dateOfBirth: string;
  roleId: number;
  isUpcomingBirthday: boolean;
}

export interface WorkingHours {
  totalWorkingHours: number;
  totalActualHours: number;
}

export interface InstructorWorkingHours {
  instructorId: number;
  instructorName: string;
  totalWorkingHours: number;
  totalActualHours: number;
  profilePhoto: string;
}
