import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService } from 'src/app/shared/services';
import { CBGetResponse } from 'src/app/shared/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { DashIfEmptyPipe } from 'src/app/shared/pipe';
import { EnsembleClassesService } from 'src/app/pages/schedule-classes/pages/ensemble-class/services';
import { EnsembleClassScheduleSummaryInfo, Instruments } from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { MatTooltipModule } from '@angular/material/tooltip';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule, MatTooltipModule],
  PIPES: [DashIfEmptyPipe]
};

@Component({
  selector: 'app-view-ensemble-class',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  templateUrl: './view-ensemble-class.component.html',
  styleUrl: './view-ensemble-class.component.scss'
})
export class ViewEnsembleClassComponent extends BaseComponent implements OnInit {
  @Input() selectedEnsembleId!: number | null;
  @Input() selectedTabOption!: string | null;

  ensembleClassDetail!: EnsembleClassScheduleSummaryInfo | undefined;

  @Output() closeViewSideNav = new EventEmitter<void>();
  @Output() groupClassUpdated = new EventEmitter<void>();
  @Output() openEditSideNav = new EventEmitter<void>();

  constructor(
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly ensembleClassService: EnsembleClassesService,
    protected readonly schedulerService: SchedulerService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getEnsembleClassDetail(this.selectedEnsembleId);
  }

  getEnsembleClassDetail(id: number | null): void {
    this.showPageLoader = true;
    this.ensembleClassService
      .get<CBGetResponse<EnsembleClassScheduleSummaryInfo>>(
        `${API_URL.ensembleClassScheduleSummaries.getEnsembleClassesScheduleSummaryForView}?id=${id}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<EnsembleClassScheduleSummaryInfo>) => {
          this.ensembleClassDetail = res.result;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstrumentNames(array: Instruments[]) {
    return array
      .slice(2)
      .map(item => item.instrumentName + '(' + item.instrumentGrade + ')')
      .join(', ');
  }

  deleteEnsembleConfirmation(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Ensemble Class`,
        message: `Are you sure you want to delete this Ensemble Class?`
      }
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result.isConfirmed) {
        this.deleteEnsemble(this.ensembleClassDetail!.id);
      }
    });
  }

  deleteEnsemble(groupId: number): void {
    this.ensembleClassService
      .delete(groupId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.closeViewSideNavFun();
          this.groupClassUpdated.emit();
          this.toasterService.success(
            this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Ensemble Class')
          );
          this.cdr.detectChanges();
        }
      });
  }

  navigateToEdit(): void {
    this.openEditSideNav.emit();
  }

  closeViewSideNavFun(): void {
    this.closeViewSideNav.emit();
  }
}
