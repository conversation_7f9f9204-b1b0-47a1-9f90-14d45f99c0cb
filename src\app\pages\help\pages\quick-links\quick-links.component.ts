import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { QuickLinks } from '../../models';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { SharedModule } from 'src/app/shared/shared.module';
import { RouterModule } from '@angular/router';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { HelpService } from '../../services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService } from 'src/app/shared/services';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogRes } from 'src/app/shared/models';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatIconModule, SharedModule, RouterModule, MatTooltipModule],
  COMPONENTS: []
};

@Component({
  selector: 'app-quick-links',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './quick-links.component.html',
  styleUrl: './quick-links.component.scss'
})
export class QuickLinksComponent extends BaseComponent implements OnChanges {
  @Input() quickLinks!: Array<QuickLinks>;
  @Input() override showPageLoader!: boolean;
  @Input() isHelpPageCreation!: boolean;
  @Output() updateQuickLinks = new EventEmitter<void>();

  constructor(
    private readonly dialog: MatDialog,
    private readonly helpService: HelpService,
    private readonly toasterService: AppToasterService
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['quickLinks']?.currentValue) {
      this.quickLinks = changes['quickLinks']?.currentValue;
    }
  }

  deleteQuickLinkConfirmation(quickLinkId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Quick Link`,
        message: `Are you sure you want to delete this Quick Link?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.deleteQuickLink(quickLinkId);
      }
    });
  }

  deleteQuickLink(quickLinkId: number): void {
    this.helpService
      .delete(quickLinkId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.updateQuickLinks.emit();
          this.toasterService.success(
            this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Quick Link')
          );
        }
      });
  }
}
